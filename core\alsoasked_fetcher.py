"""
AlsoAsked API Fetcher - Alternative Data Source

This module provides functionality to fetch data from the AlsoAsked API
as an alternative to SERP API for content research and analysis.

AlsoAsked provides "People Also Ask" data and related questions
which can be valuable for content planning and SEO research.
"""

import requests
import json
from typing import Dict, Any, List, Optional


def fetch_data(query: str, api_key: str, region: str = "US", language: str = "en") -> Dict[str, Any]:
    """
    Fetch data from AlsoAsked API for the given query.
    
    Args:
        query: The search query/keyword
        api_key: AlsoAsked API key
        region: Region code (default: "US")
        language: Language code (default: "en")
        
    Returns:
        Dictionary containing the AlsoAsked API response data
        
    Raises:
        Exception: If the API request fails or returns an error
    """
    # AlsoAsked API endpoint
    url = "https://alsoasked.com/api/v1/search"
    
    # Request parameters
    params = {
        "query": query,
        "region": region,
        "language": language
    }
    
    # Request headers
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Fetching AlsoAsked data for query: '{query}'")
        
        # Make the API request
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        # Check if request was successful
        if response.status_code == 200:
            data = response.json()
            print(f"✓ AlsoAsked API request successful")
            return data
        elif response.status_code == 401:
            raise Exception("AlsoAsked API authentication failed. Please check your API key.")
        elif response.status_code == 429:
            raise Exception("AlsoAsked API rate limit exceeded. Please try again later.")
        else:
            raise Exception(f"AlsoAsked API request failed with status {response.status_code}: {response.text}")
            
    except requests.exceptions.Timeout:
        raise Exception("AlsoAsked API request timed out")
    except requests.exceptions.ConnectionError:
        raise Exception("Failed to connect to AlsoAsked API")
    except requests.exceptions.RequestException as e:
        raise Exception(f"AlsoAsked API request error: {str(e)}")


def test_connection(api_key: str) -> bool:
    """
    Test the connection to AlsoAsked API.
    
    Args:
        api_key: AlsoAsked API key to test
        
    Returns:
        True if connection is successful, False otherwise
    """
    try:
        # Use a simple test query
        test_data = fetch_data("test query", api_key)
        
        # Check if we got a valid response structure
        if isinstance(test_data, dict):
            print("AlsoAsked API connection test successful")
            return True
        else:
            print("AlsoAsked API connection test failed - invalid response format")
            return False
            
    except Exception as e:
        print(f"AlsoAsked API connection test failed: {e}")
        return False


def format_for_analysis(alsoasked_data: Dict[str, Any], keyword: str) -> str:
    """
    Format AlsoAsked data for LLM analysis.
    
    Args:
        alsoasked_data: Raw data from AlsoAsked API
        keyword: The original search keyword
        
    Returns:
        Formatted string suitable for LLM analysis
    """
    try:
        formatted_text = f"AlsoAsked Data Analysis for '{keyword}':\n\n"
        
        # Extract and format the "People Also Ask" questions
        if 'questions' in alsoasked_data:
            questions = alsoasked_data['questions']
            if questions:
                formatted_text += "People Also Ask Questions:\n"
                for i, question in enumerate(questions[:10], 1):  # Limit to top 10
                    if isinstance(question, dict) and 'question' in question:
                        formatted_text += f"{i}. {question['question']}\n"
                    elif isinstance(question, str):
                        formatted_text += f"{i}. {question}\n"
                formatted_text += "\n"
        
        # Extract related topics if available
        if 'related_topics' in alsoasked_data:
            topics = alsoasked_data['related_topics']
            if topics:
                formatted_text += "Related Topics:\n"
                for i, topic in enumerate(topics[:5], 1):  # Limit to top 5
                    if isinstance(topic, dict) and 'topic' in topic:
                        formatted_text += f"• {topic['topic']}\n"
                    elif isinstance(topic, str):
                        formatted_text += f"• {topic}\n"
                formatted_text += "\n"
        
        # Add search volume data if available
        if 'search_volume' in alsoasked_data:
            volume = alsoasked_data['search_volume']
            formatted_text += f"Search Volume: {volume}\n\n"
        
        # Add any additional insights
        if 'insights' in alsoasked_data:
            insights = alsoasked_data['insights']
            if insights:
                formatted_text += "Key Insights:\n"
                for insight in insights:
                    formatted_text += f"• {insight}\n"
                formatted_text += "\n"
        
        return formatted_text
        
    except Exception as e:
        print(f"Error formatting AlsoAsked data: {e}")
        # Return a basic fallback format
        return f"AlsoAsked Data for '{keyword}':\n{json.dumps(alsoasked_data, indent=2)}"


# Stub implementation note:
# This is a basic implementation structure for AlsoAsked API integration.
# The actual API endpoints, parameters, and response format may differ
# from what's implemented here. This serves as a foundation that can be
# updated once the actual AlsoAsked API documentation is consulted.

if __name__ == '__main__':
    print("Testing AlsoAsked API Fetcher...")
    
    # This would require a valid API key to test
    test_api_key = "YOUR_ALSOASKED_API_KEY"
    
    if test_api_key != "YOUR_ALSOASKED_API_KEY":
        try:
            # Test connection
            if test_connection(test_api_key):
                print("✓ Connection test passed")
                
                # Test data fetching
                data = fetch_data("best shaving brush", test_api_key)
                print(f"✓ Data fetch successful: {len(str(data))} characters")
                
                # Test formatting
                formatted = format_for_analysis(data, "best shaving brush")
                print(f"✓ Data formatting successful: {len(formatted)} characters")
                
            else:
                print("✗ Connection test failed")
                
        except Exception as e:
            print(f"✗ Test failed: {e}")
    else:
        print("Skipping tests - no API key provided")
        print("To test, replace 'YOUR_ALSOASKED_API_KEY' with a valid API key")
    
    print("AlsoAsked API Fetcher testing complete!")
