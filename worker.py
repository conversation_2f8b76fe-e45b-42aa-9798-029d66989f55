"""
Worker - The Executor

This module executes a single planned job by performing the sequence of tasks:
1. SERP analysis using the salvaged core utilities
2. Blog writing using a master prompt
3. Publishing the draft to the chosen platform (Shopify/WordPress)
4. Updating the job status to PUBLISHED in the database
"""

import re
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from database import ContentDatabase
from core import serp_fetcher, config_manager
from core.llm_interface import get_llm_instance
from core import shopify_poster, wordpress_poster


class ContentWorker:
    """
    The Executor that processes a single planned content job.
    """
    
    def __init__(self):
        """Initialize the worker with database connection."""
        self.db = ContentDatabase()
    
    def execute_job(self, job: Dict[str, Any], run_data_collection: bool = True,
                   run_blog_writing: bool = True, run_publish: bool = True,
                   use_serpapi: bool = True, use_alsoasked: bool = False, use_llm_suggestion: bool = False,
                   publish_platform: str = "Shopify", publish_as_draft: bool = True) -> Dict[str, Any]:
        """
        Execute a complete content job with configurable steps.

        Args:
            job: The job dictionary from the planner (content record)
            run_data_collection: Whether to perform SERP/data collection
            run_blog_writing: Whether to write blog content
            run_publish: Whether to publish to platform
            use_serpapi: Whether to use SerpApi data source
            use_alsoasked: Whether to use AlsoAsked data source
            use_llm_suggestion: Whether to use LLM-only suggestions
            publish_platform: Platform to publish to ("Shopify", "WordPress")
            publish_as_draft: Whether to publish as draft (True) or live (False)

        Returns:
            Dictionary with execution results and status
        """
        job_id = job['id']
        keyword = job['keyword']

        print(f"Starting job execution for: '{keyword}' (ID: {job_id})")
        print(f"Steps enabled: Data Collection={run_data_collection}, Writing={run_blog_writing}, Publishing={run_publish}")
        print(f"Data sources: SerpApi={use_serpapi}, AlsoAsked={use_alsoasked}, LLM={use_llm_suggestion}")
        print(f"Publishing: Platform={publish_platform}, Draft={publish_as_draft}")
        
        # Extract LLM overrides from job if present
        override_writing_llm = job.get('override_writing_llm')
        override_writing_model = job.get('override_writing_model')
        override_analysis_llm = job.get('override_analysis_llm') or override_writing_llm  # Use writing LLM for analysis if no separate override
        override_analysis_model = job.get('override_analysis_model') or override_writing_model
        
        if override_writing_llm and override_writing_model:
            print(f"Using LLM overrides: {override_writing_llm} ({override_writing_model}) for writing")
        if override_analysis_llm and override_analysis_model and (override_analysis_llm != override_writing_llm or override_analysis_model != override_writing_model):
            print(f"Using LLM overrides: {override_analysis_llm} ({override_analysis_model}) for analysis")

        # Update status to WRITING
        self.db.update_content_status(job_id, 'WRITING')

        result = {
            'job_id': job_id,
            'keyword': keyword,
            'success': False,
            'steps_completed': [],
            'errors': [],
            'serp_data': None,
            'analysis_results': None,
            'blog_content': None,
            'platform_url': None
        }

        try:
            # Step 1: SERP Analysis (conditional)
            if run_data_collection:
                print("Step 1: Performing SERP/data analysis...")
                serp_data, analysis_results = self._perform_serp_analysis(
                    keyword, use_serpapi, use_alsoasked, use_llm_suggestion, 
                    override_analysis_llm, override_analysis_model
                )
                result['serp_data'] = serp_data
                result['analysis_results'] = analysis_results
                result['steps_completed'].append('serp_analysis')
                print("✓ SERP/data analysis completed")
            else:
                print("Step 1: Skipping SERP/data analysis (disabled)")
                # Create minimal analysis for writing step
                analysis_results = {
                    'raw_analysis': f"No SERP analysis performed. Focus on creating quality content about {keyword}.",
                    'keyword': keyword,
                    'llm_type': override_analysis_llm or 'none',
                    'model_id': override_analysis_model or 'none'
                }
                result['analysis_results'] = analysis_results

            # Step 2: Blog Writing (conditional)
            if run_blog_writing:
                print("Step 2: Writing blog content...")
                blog_content, meta_description = self._write_blog_content(
                    keyword, analysis_results, job.get('proposed_angle', ''),
                    override_writing_llm, override_writing_model
                )
                result['blog_content'] = blog_content
                result['meta_description'] = meta_description
                result['steps_completed'].append('blog_writing')
                
                # Store generated content in database regardless of publishing
                if blog_content:
                    self.db.update_generated_content(job_id, blog_content)
                    print("✓ Blog content written and stored in database")
                else:
                    print("✓ Blog content writing completed (empty content)")
            else:
                print("Step 2: Skipping blog writing (disabled)")
                blog_content = ""
                meta_description = ""

            # Step 3: Publishing (conditional)
            if run_publish and blog_content:
                print(f"Step 3: Publishing content to {publish_platform} ({'draft' if publish_as_draft else 'live'})...")
                platform_url = self._publish_content(keyword, blog_content, meta_description, job, publish_platform, publish_as_draft)
                result['platform_url'] = platform_url
                result['steps_completed'].append('publishing')
                
                if publish_as_draft:
                    print(f"✓ Content published as DRAFT at: {platform_url}")
                else:
                    print(f"✓ Content published LIVE at: {platform_url}")

                # Step 4: Update database to PUBLISHED
                print("Step 4: Updating database to PUBLISHED...")
                self.db.update_content_published(job_id, platform_url)
                result['steps_completed'].append('database_update')
                print("✓ Database updated to PUBLISHED")
            elif run_publish and not blog_content:
                print("Step 3: Cannot publish - no blog content available")
                result['errors'].append("Cannot publish - no blog content available")
            else:
                print("Step 3: Skipping publishing (disabled)")
                # Update database to WRITTEN_NOT_PUBLISHED if content was written but not published
                if blog_content:
                    print("Step 4: Updating database to WRITTEN_NOT_PUBLISHED...")
                    self.db.update_content_status(job_id, 'WRITTEN_NOT_PUBLISHED')
                    result['steps_completed'].append('database_update')
                    print("✓ Database updated to WRITTEN_NOT_PUBLISHED")

            result['success'] = True
            print(f"Job '{keyword}' completed successfully!")

        except Exception as e:
            error_msg = f"Job execution failed: {str(e)}"
            print(f"✗ {error_msg}")
            result['errors'].append(error_msg)

            # Update status back to ON_HOLD on failure
            self.db.update_content_status(job_id, 'ON_HOLD')

        return result
    
    def _perform_serp_analysis(self, keyword: str, use_serpapi: bool = True, use_alsoasked: bool = False, use_llm_suggestion: bool = False, override_llm_type: Optional[str] = None, override_model_id: Optional[str] = None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Perform SERP data fetching and analysis using multiple data sources concurrently.

        Args:
            keyword: The target keyword
            use_serpapi: Whether to use SerpApi data source
            use_alsoasked: Whether to use AlsoAsked data source  
            use_llm_suggestion: Whether to use LLM-only suggestions
            override_llm_type: Override LLM provider for analysis
            override_model_id: Override LLM model for analysis

        Returns:
            Tuple of (all_serp_data, combined_analysis_results)
        """
        # Initialize aggregation containers
        all_serp_data = {}
        all_analysis_texts = []
        
        print(f"📊 Collecting data for '{keyword}' from multiple sources...")
        print(f"   SerpApi: {use_serpapi}, AlsoAsked: {use_alsoasked}, LLM: {use_llm_suggestion}")
        
        # Fetch from SerpApi if enabled
        if use_serpapi:
            try:
                serp_api_key = config_manager.get_api_key('serpapi_key')
                if not serp_api_key:
                    print("⚠️ SerpApi key not found, skipping SerpApi data")
                else:
                    print("🔍 Fetching SerpApi data...")
                    # Get SERP settings
                    serp_type = config_manager.get_pipeline_setting('serp_type') or 'Combined (Search + Autocomplete)'
                    country_code = config_manager.get_pipeline_setting('serp_country_code') or 'us'
                    language_code = config_manager.get_pipeline_setting('serp_language_code') or 'en'
                    max_calls = config_manager.get_pipeline_setting('serp_api_max_calls') or 1

                    # Fetch SERP data
                    if 'Combined' in serp_type:
                        serp_api_data = serp_fetcher.fetch_combined_data(
                            query=keyword,
                            api_key=serp_api_key,
                            gl=country_code,
                            hl=language_code,
                            max_calls=max_calls
                        )
                    else:
                        serp_api_data = serp_fetcher.fetch_serp_data(
                            query=keyword,
                            api_key=serp_api_key,
                            gl=country_code,
                            hl=language_code,
                            max_calls=max_calls
                        )
                    
                    all_serp_data['serpapi'] = serp_api_data
                    
                    # Analyze SerpApi data
                    serp_analysis = self._analyze_serp_data(serp_api_data, keyword, override_llm_type, override_model_id)
                    all_analysis_texts.append(f"=== SERPAPI ANALYSIS ===\n{serp_analysis['raw_analysis']}")
                    print("✅ SerpApi data collected and analyzed")
                    
            except Exception as e:
                print(f"❌ Error fetching SerpApi data: {e}")
                all_analysis_texts.append(f"=== SERPAPI ERROR ===\nFailed to fetch SerpApi data: {e}")
        
        # Fetch from AlsoAsked if enabled
        if use_alsoasked:
            try:
                from core import alsoasked_fetcher
                alsoasked_api_key = config_manager.get_api_key('alsoasked_key')
                if not alsoasked_api_key:
                    print("⚠️ AlsoAsked key not found, skipping AlsoAsked data")
                else:
                    print("🔍 Fetching AlsoAsked data...")
                    # Fetch AlsoAsked data
                    alsoasked_data = alsoasked_fetcher.fetch_data(keyword, alsoasked_api_key)
                    formatted_data = alsoasked_fetcher.format_for_analysis(alsoasked_data, keyword)
                    
                    all_serp_data['alsoasked'] = alsoasked_data
                    
                    # Analyze AlsoAsked data
                    alsoasked_analysis = self._analyze_alsoasked_data(formatted_data, keyword, override_llm_type, override_model_id)
                    all_analysis_texts.append(f"=== ALSOASKED ANALYSIS ===\n{alsoasked_analysis['raw_analysis']}")
                    print("✅ AlsoAsked data collected and analyzed")
                    
            except Exception as e:
                print(f"❌ Error fetching AlsoAsked data: {e}")
                all_analysis_texts.append(f"=== ALSOASKED ERROR ===\nFailed to fetch AlsoAsked data: {e}")
        
        # Generate LLM suggestions if enabled
        if use_llm_suggestion:
            try:
                print("🧠 Generating LLM-only analysis...")
                llm_analysis = self._analyze_llm_only(keyword, override_llm_type, override_model_id)
                all_serp_data['llm_suggestion'] = {"source": "LLM_ONLY", "keyword": keyword}
                all_analysis_texts.append(f"=== LLM ANALYSIS ===\n{llm_analysis['raw_analysis']}")
                print("✅ LLM analysis completed")
                
            except Exception as e:
                print(f"❌ Error generating LLM analysis: {e}")
                all_analysis_texts.append(f"=== LLM ERROR ===\nFailed to generate LLM analysis: {e}")
        
        # If no sources were used, fallback to LLM-only
        if not all_analysis_texts:
            print("⚠️ No data sources enabled, falling back to LLM-only analysis")
            try:
                llm_analysis = self._analyze_llm_only(keyword, override_llm_type, override_model_id)
                all_serp_data['llm_fallback'] = {"source": "LLM_FALLBACK", "keyword": keyword}
                all_analysis_texts.append(f"=== LLM FALLBACK ANALYSIS ===\n{llm_analysis['raw_analysis']}")
            except Exception as e:
                all_analysis_texts.append(f"=== CRITICAL ERROR ===\nAll data sources failed: {e}")
        
        # Combine all analysis results
        combined_analysis = "\n\n".join(all_analysis_texts)
        
        # Create final analysis results
        analysis_results = {
            'raw_analysis': combined_analysis,
            'keyword': keyword,
            'sources_used': list(all_serp_data.keys()),
            'llm_type': override_llm_type or config_manager.get_pipeline_setting('analysis_llm') or 'openai',
            'model_id': override_model_id or config_manager.get_pipeline_setting('analysis_llm_model')
        }
        
        print(f"📊 Data collection complete for '{keyword}' - {len(all_serp_data)} sources used")
        return all_serp_data, analysis_results
    
    def _analyze_serp_data(self, serp_data: Dict[str, Any], keyword: str, override_llm_type: Optional[str] = None, override_model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze SERP data using LLM.
        
        Args:
            serp_data: The SERP data from SerpAPI
            keyword: The target keyword
            override_llm_type: Override LLM provider for analysis
            override_model_id: Override LLM model for analysis
            
        Returns:
            Analysis results dictionary
        """
        # Get LLM configuration for analysis (with overrides)
        llm_type = override_llm_type or config_manager.get_pipeline_setting('analysis_llm') or 'openai'
        model_id = override_model_id or config_manager.get_pipeline_setting('analysis_llm_model')
        
        # Get API key
        api_key = None
        api_endpoint = None
        
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')
        
        # Get analysis prompt
        analysis_prompt_template = config_manager.get_prompt('analysis_prompt')
        if not analysis_prompt_template:
            raise ValueError("Analysis prompt not found in configuration")
        
        # Format the prompt
        prompt = analysis_prompt_template.format(
            keyword=keyword,
            serp_data=serp_data
        )
        
        # Get LLM instance and generate analysis
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        analysis_text = llm.generate(prompt, max_tokens=1500)
        
        return {
            'raw_analysis': analysis_text,
            'keyword': keyword,
            'llm_type': llm_type,
            'model_id': model_id
        }

    def _analyze_alsoasked_data(self, formatted_data: str, keyword: str, override_llm_type: Optional[str] = None, override_model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze AlsoAsked data using LLM.

        Args:
            formatted_data: Formatted AlsoAsked data
            keyword: The target keyword
            override_llm_type: Override LLM provider for analysis
            override_model_id: Override LLM model for analysis

        Returns:
            Analysis results dictionary
        """
        # Get LLM configuration for analysis (with overrides)
        llm_type = override_llm_type or config_manager.get_pipeline_setting('analysis_llm') or 'openai'
        model_id = override_model_id or config_manager.get_pipeline_setting('analysis_llm_model')

        # Get API key and endpoint
        api_key, api_endpoint = self._get_llm_credentials(llm_type)

        # Create analysis prompt for AlsoAsked data
        prompt = f"""Analyze the following AlsoAsked data for the keyword '{keyword}' and provide insights for content creation:

{formatted_data}

Please provide:
1. Key themes and topics people are asking about
2. Content gaps and opportunities
3. Recommended content angles based on the questions
4. SEO insights and keyword suggestions
5. Target audience interests and pain points

Focus on actionable insights that can guide content creation."""

        # Get LLM instance and generate analysis
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        analysis_text = llm.generate(prompt, max_tokens=1500)

        return {
            'raw_analysis': analysis_text,
            'keyword': keyword,
            'llm_type': llm_type,
            'model_id': model_id,
            'data_source': 'AlsoAsked'
        }

    def _analyze_llm_only(self, keyword: str, override_llm_type: Optional[str] = None, override_model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform LLM-only analysis without external data sources.

        Args:
            keyword: The target keyword
            override_llm_type: Override LLM provider for analysis
            override_model_id: Override LLM model for analysis

        Returns:
            Analysis results dictionary
        """
        # Get LLM configuration for analysis (with overrides)
        llm_type = override_llm_type or config_manager.get_pipeline_setting('analysis_llm') or 'openai'
        model_id = override_model_id or config_manager.get_pipeline_setting('analysis_llm_model')

        # Get API key and endpoint
        api_key, api_endpoint = self._get_llm_credentials(llm_type)

        # Create analysis prompt for LLM-only analysis
        prompt = f"""Provide a comprehensive content analysis for the keyword '{keyword}' based on your knowledge:

Please provide:
1. Key topics and subtopics related to '{keyword}'
2. Common questions people ask about this topic
3. Content angles and approaches that would be valuable
4. Target audience considerations
5. SEO recommendations and related keywords
6. Potential content structure suggestions

Focus on creating valuable, informative content that addresses user intent and provides genuine value."""

        # Get LLM instance and generate analysis
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        analysis_text = llm.generate(prompt, max_tokens=1500)

        return {
            'raw_analysis': analysis_text,
            'keyword': keyword,
            'llm_type': llm_type,
            'model_id': model_id,
            'data_source': 'LLM_Only'
        }

    def _get_llm_credentials(self, llm_type: str) -> tuple:
        """
        Get LLM credentials based on type.

        Args:
            llm_type: The LLM provider type

        Returns:
            Tuple of (api_key, api_endpoint)
        """
        api_key = None
        api_endpoint = None

        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')

        return api_key, api_endpoint
    
    def _write_blog_content(self, keyword: str, analysis_results: Dict[str, Any], 
                           proposed_angle: str, override_llm_type: Optional[str] = None, override_model_id: Optional[str] = None) -> Tuple[str, str]:
        """
        Write blog content using LLM.
        
        Args:
            keyword: The target keyword
            analysis_results: The SERP analysis results
            proposed_angle: The proposed content angle
            override_llm_type: Override LLM provider for writing
            override_model_id: Override LLM model for writing
            
        Returns:
            Tuple of (blog_content_html, meta_description)
        """
        # Get LLM configuration for writing (with overrides)
        llm_type = override_llm_type or config_manager.get_pipeline_setting('writing_llm') or 'openai'
        model_id = override_model_id or config_manager.get_pipeline_setting('writing_llm_model')
        
        # Get API key
        api_key = None
        api_endpoint = None
        
        if llm_type == 'openai':
            api_key = config_manager.get_api_key('openai_key')
        elif llm_type == 'gemini':
            api_key = config_manager.get_api_key('gemini_key')
        elif llm_type == 'anthropic':
            api_key = config_manager.get_api_key('anthropic_key')
        elif llm_type == 'local':
            api_endpoint = config_manager.get_api_key('local_llm_endpoint')
        elif llm_type == 'openrouter':
            api_key = config_manager.get_api_key('openrouter_key')
        elif llm_type == 'groq':
            api_key = config_manager.get_api_key('groq_key')
        
        # Get writing settings
        word_count = config_manager.get_pipeline_setting('blog_word_count') or 1000
        tone = config_manager.get_pipeline_setting('blog_tone') or 'Professional'
        
        # Get writing prompt
        writing_prompt_template = config_manager.get_prompt('writing_prompt')
        if not writing_prompt_template:
            raise ValueError("Writing prompt not found in configuration")
        
        # Format the prompt
        prompt = writing_prompt_template.format(
            keyword=keyword,
            analysis_results=analysis_results['raw_analysis'],
            word_count=word_count,
            gatekeeper_feedback=""  # No gatekeeper in new architecture
        )
        
        # Get LLM instance and generate content
        llm = get_llm_instance(llm_type, api_key=api_key, model_id=model_id, api_endpoint=api_endpoint)
        response = llm.generate(prompt, max_tokens=2500)
        
        # Extract meta description and content
        meta_description = ""
        blog_content = response
        
        # Look for META_DESCRIPTION: at the start
        if response.startswith("META_DESCRIPTION:"):
            lines = response.split('\n', 1)
            if len(lines) >= 2:
                meta_description = lines[0].replace("META_DESCRIPTION:", "").strip()
                blog_content = lines[1].strip()
        
        return blog_content, meta_description
    
    def _publish_content(self, keyword: str, blog_content: str, meta_description: str,
                        job: Dict[str, Any], platform: str = "Shopify", publish_as_draft: bool = True) -> str:
        """
        Publish content to the specified platform.

        Args:
            keyword: The target keyword (used as title base)
            blog_content: The HTML blog content
            meta_description: The meta description
            job: The job dictionary with additional context
            platform: Platform to publish to ("Shopify" or "WordPress")
            publish_as_draft: Whether to publish as draft (True) or live (False)

        Returns:
            The URL where the content was published
        """
        # Generate title from keyword and proposed angle
        title = self._generate_title(keyword, job.get('proposed_angle', ''))

        # Generate tags
        tags = self._generate_tags(job)

        if platform == "Shopify":
            return self._publish_to_shopify(title, blog_content, meta_description, tags, publish_as_draft)
        elif platform == "WordPress":
            return self._publish_to_wordpress(title, blog_content, meta_description, tags, publish_as_draft)
        else:
            raise ValueError(f"Unsupported platform: {platform}")
    
    def _generate_title(self, keyword: str, proposed_angle: str) -> str:
        """Generate a title from keyword and proposed angle."""
        # Simple title generation - could be enhanced with LLM
        title_words = keyword.split()
        title = ' '.join(word.capitalize() for word in title_words)
        
        # Add context from angle if available
        if proposed_angle and len(proposed_angle) > 20:
            # Extract key phrases from angle
            if 'guide' in proposed_angle.lower():
                title = f"The Complete Guide to {title}"
            elif 'how to' in proposed_angle.lower():
                title = f"How to Choose the Best {title}"
            elif 'best' in proposed_angle.lower():
                title = f"Best {title} for Quality and Value"
            else:
                title = f"Everything You Need to Know About {title}"
        
        return title
    
    def _generate_tags(self, job: Dict[str, Any]) -> str:
        """Generate tags for the content."""
        tags = []
        
        # Add pillar and craft as tags
        if job.get('pillar'):
            tags.append(job['pillar'])
        if job.get('craft'):
            tags.append(job['craft'])
        
        # Add some default Stuga tags
        tags.extend(['SEO', 'Quality', 'Australian Made'])
        
        # Add additional tags from config
        additional_tags = config_manager.get_pipeline_setting('shopify_additional_tags')
        if additional_tags:
            tags.extend([tag.strip() for tag in additional_tags.split(',') if tag.strip()])
        
        return ', '.join(tags)
    
    def _publish_to_shopify(self, title: str, content: str, meta_description: str, tags: str, publish_as_draft: bool = True) -> str:
        """Publish content to Shopify."""
        shop_url = config_manager.get_setting('SHOPIFY', 'shop_url')
        api_token = config_manager.get_setting('SHOPIFY', 'api_token')
        
        if not shop_url or not api_token:
            raise ValueError("Shopify credentials not configured")
        
        # Use GUI control for draft/live, ignoring config setting
        shopify_published_status = not publish_as_draft  # published=True means live for Shopify
        
        # Check if should upload meta description
        upload_meta = config_manager.get_setting('SHOPIFY', 'upload_meta_description')
        meta_to_upload = meta_description if upload_meta else None
        
        article = shopify_poster.post_blog_article(
            shop_url=shop_url,
            api_token=api_token,
            title=title,
            content_html=content,
            author="Stuga Content Team",
            tags=tags,
            published=shopify_published_status,
            meta_description=meta_to_upload
        )
        
        # Construct the URL (this is a simplified version)
        shop_domain = shop_url.replace('https://', '').replace('/', '')
        return f"https://{shop_domain}/blogs/news/{article.id}"
    
    def _publish_to_wordpress(self, title: str, content: str, meta_description: str, tags: str, publish_as_draft: bool = True) -> str:
        """Publish content to WordPress."""
        site_url = config_manager.get_setting('WORDPRESS', 'site_url')
        username = config_manager.get_setting('WORDPRESS', 'username')
        
        if not site_url or not username:
            raise ValueError("WordPress credentials not configured")
        
        # Set WordPress status based on draft preference
        wp_status = 'draft' if publish_as_draft else 'publish'
        
        # This would need to be implemented based on wordpress_poster.py
        # The wordpress_poster.post_blog_article should accept published_status parameter
        # For now, return a placeholder that indicates the intended status
        return f"{site_url}/blog/{wp_status}-post"


# Convenience function
def execute_content_job(job: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to execute a content job.
    
    Args:
        job: The job dictionary from the planner
        
    Returns:
        Dictionary with execution results
    """
    worker = ContentWorker()
    return worker.execute_job(job)


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Worker...")
    
    # This would typically be called with a job from the planner
    # For testing, we'll create a mock job
    mock_job = {
        'id': 1,
        'keyword': 'best shaving brush',
        'pillar': 'shaving brushes',
        'craft': 'grooming',
        'proposed_angle': 'Complete guide to choosing quality shaving brushes for beginners',
        'status': 'PLANNED'
    }
    
    try:
        worker = ContentWorker()
        result = worker.execute_job(mock_job)
        
        print(f"Job execution result:")
        print(f"Success: {result['success']}")
        print(f"Steps completed: {result['steps_completed']}")
        if result['errors']:
            print(f"Errors: {result['errors']}")
        if result['platform_url']:
            print(f"Published at: {result['platform_url']}")
            
    except Exception as e:
        print(f"Could not test worker: {e}")
        print("Make sure all API keys and settings are configured in config.ini")
    
    print("\nContent Worker testing complete!")
