#!/usr/bin/env python3
"""
Test Script for Enhanced Freshness Scoring Debug

This script tests the enhanced debugging capabilities we've added to the 
Content Strategist's freshness scoring system.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import ContentDatabase
from idea_generator import IdeaGenerator
from planner import ContentPlanner

def test_embedding_status():
    """Test embedding model status reporting."""
    print("🧠 TESTING EMBEDDING STATUS")
    print("=" * 50)
    
    # Test idea generator embedding status
    idea_gen = IdeaGenerator()
    print(f"Idea Generator Embedding Status: {idea_gen.get_embedding_status()}")
    
    # Test planner embedding status
    planner = ContentPlanner(debug_mode=True)
    print(f"Content Planner Embedding Status: {planner.get_embedding_status()}")
    
    print()

def test_database_content():
    """Test basic database functionality and show content stats."""
    print("📊 TESTING DATABASE CONTENT")
    print("=" * 50)
    
    db = ContentDatabase()
    stats = db.get_content_stats()
    
    print("Database Statistics:")
    for status, count in stats.items():
        print(f"  • {status}: {count}")
    
    total = sum(stats.values())
    print(f"  • TOTAL: {total}")
    
    if total == 0:
        print("\n⚠️  Database is empty. Adding some test content...")
        add_test_content(db)
        stats = db.get_content_stats()
        print("Updated Statistics:")
        for status, count in stats.items():
            print(f"  • {status}: {count}")
    
    print()

def add_test_content(db):
    """Add some test content ideas to the database."""
    test_ideas = [
        {
            'keyword': 'best shaving brush',
            'pillar': 'grooming tools',
            'craft': 'grooming',
            'angle': 'Complete guide to choosing quality badger hair shaving brushes for traditional wet shaving'
        },
        {
            'keyword': 'natural beard oil',
            'pillar': 'beard care',
            'craft': 'grooming',
            'angle': 'Benefits of natural ingredients in beard conditioning oils vs synthetic alternatives'
        },
        {
            'keyword': 'leather wallet care',
            'pillar': 'leather maintenance',
            'craft': 'leather goods',
            'angle': 'How to properly maintain and condition leather wallets to last decades'
        },
    ]
    
    for idea in test_ideas:
        db.insert_content_idea(
            keyword=idea['keyword'],
            pillar=idea['pillar'],
            craft=idea['craft'],
            proposed_angle=idea['angle']
        )
    
    print("✅ Added 3 test content ideas to database")

def test_freshness_scoring():
    """Test the enhanced freshness scoring with debug output."""
    print("🎯 TESTING FRESHNESS SCORING WITH DEBUG OUTPUT")
    print("=" * 50)
    
    # Initialize planner with debug mode enabled
    planner = ContentPlanner(debug_mode=True)
    
    # Test pillar weights
    test_weights = {
        'grooming': 1.2,
        'leather goods': 1.0,
        'fragrance': 0.8
    }
    
    print("Calculating freshness scores with debug mode enabled...")
    print("(Watch for detailed debug output below)")
    print("-" * 50)
    
    # Calculate scores - this will generate lots of debug output
    scored_content = planner.calculate_freshness_scores(test_weights)
    
    print("-" * 50)
    print(f"✅ Scoring completed for {len(scored_content)} items")
    
    if scored_content:
        print("\nTop scored items:")
        for i, item in enumerate(scored_content[:3]):
            keyword = item.get('keyword', 'Unknown')
            score = item.get('freshness_score', 0)
            print(f"  {i+1}. '{keyword}': {score:.1f}")
    
    # Test debug info retrieval
    debug_info = planner.get_scoring_debug_info()
    if debug_info:
        print(f"\n📋 Debug Info Summary:")
        print(f"  • Embedding Status: {debug_info.get('embedding_status', 'Unknown')}")
        print(f"  • Content Scored: {debug_info.get('content_count', 0)}")
        print(f"  • Published Content: {debug_info.get('published_count', 0)}")
        
        zero_scores = debug_info.get('zero_scores', [])
        if zero_scores:
            print(f"  • Items with 0.0 scores: {len(zero_scores)}")
            for keyword in zero_scores[:3]:  # Show first 3
                print(f"    - {keyword}")
            if len(zero_scores) > 3:
                print(f"    - ... and {len(zero_scores) - 3} more")
        else:
            print("  • No items with 0.0 scores found ✅")
    
    print()

def test_job_selection():
    """Test job selection with different thresholds."""
    print("🚀 TESTING JOB SELECTION")
    print("=" * 50)
    
    planner = ContentPlanner(debug_mode=False)  # Less verbose for this test
    
    test_weights = {
        'grooming': 1.2,
        'leather goods': 1.0,
        'fragrance': 0.8
    }
    
    # Test with different thresholds
    thresholds = [90.0, 70.0, 50.0, 10.0]
    
    for threshold in thresholds:
        print(f"Testing threshold: {threshold}")
        
        # Reset any previously planned jobs back to NEW
        db = ContentDatabase()
        planned_content = db.get_content_by_status('PLANNED')
        for content in planned_content:
            db.update_content_status(content['id'], 'NEW')
        
        # Try to select a job
        job = planner.select_next_job(test_weights, threshold)
        
        if job:
            print(f"  ✅ Selected: '{job['keyword']}' (Score: {job['freshness_score']:.1f})")
        else:
            print(f"  ❌ No job meets threshold {threshold}")
    
    print()

def main():
    """Run all tests."""
    print("🔍 ENHANCED FRESHNESS SCORING DEBUG TESTS")
    print("=" * 50)
    print("This script tests the enhanced debugging capabilities for freshness scoring.")
    print("Look for detailed debug output with emoji indicators!\n")
    
    try:
        test_embedding_status()
        test_database_content()
        test_freshness_scoring()
        test_job_selection()
        
        print("🎉 ALL TESTS COMPLETED!")
        print("\n💡 Key Debugging Features:")
        print("  • Embedding model status tracking with detailed error messages")
        print("  • Component-level score debugging (Time, Uniqueness, Angle, Pillar)")
        print("  • Detailed logging for each content idea scoring process")
        print("  • Debug info storage for GUI display")
        print("  • Clear identification of 0.0 score causes")
        print("\n🚀 To use in GUI:")
        print("  • Run the application: python main_gui.py")
        print("  • Use Tools > 🔍 Scoring Debug Info for detailed analysis")
        print("  • Use Tools > 🧠 Embedding Status to check model status")
        print("  • Watch the Log tab for real-time debug output")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
