"""
Planner - The Strategic Brain

This module analyzes the idea queue and decides what to write next based on freshness scoring.
It calculates Final Freshness Scores using the formula:
Final Score = ((TimeScore * 0.5) + (UniquenessScore * 0.5)) * PillarWeight + AngleBonus
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
import traceback

# Try to import heavy ML dependencies with fallbacks
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("Warning: numpy not available. Using simple vector calculations.")

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("Warning: sentence-transformers not available. Using keyword-based similarity.")

from database import ContentDatabase, deserialize_vector


class ContentPlanner:
    """
    The Strategic Brain that analyzes content ideas and selects what to write next.
    Enhanced with comprehensive debugging for freshness scoring issues.
    """
    
    def __init__(self, debug_mode: bool = True):
        """Initialize the planner with database connection and debugging."""
        self.db = ContentDatabase()
        self.embedding_model = None
        self.embedding_status = "Unknown"
        self.debug_mode = debug_mode
        self.last_scoring_debug = []  # Store debug info from last scoring run
        self._load_embedding_model()
    
    def _load_embedding_model(self):
        """Load the sentence transformer model for similarity calculations with enhanced debugging."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            self.embedding_status = "SentenceTransformers library not installed"
            print(f"🚨 EMBEDDING DEBUG: {self.embedding_status}")
            print("   Install with: pip install sentence-transformers")
            self.embedding_model = None
            return
            
        try:
            print("🔄 EMBEDDING DEBUG: Loading sentence transformer model for planner...")
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.embedding_status = "Successfully loaded all-MiniLM-L6-v2"
            print(f"✅ EMBEDDING DEBUG: {self.embedding_status}")
        except Exception as e:
            self.embedding_status = f"Failed to load: {str(e)}"
            print(f"🚨 EMBEDDING DEBUG: {self.embedding_status}")
            print(f"   Full error: {e}")
            print("   Uniqueness scoring will use simplified keyword-based fallback")
            self.embedding_model = None
    
    def get_embedding_status(self) -> str:
        """Get the current embedding model status for debugging."""
        return self.embedding_status
    
    def calculate_time_score(self, target_idea: Dict[str, Any], all_published_content: List[Dict[str, Any]], max_age_days: int = 30) -> float:
        """
        Calculate time-based freshness score (0-100) based on similarity to published content.
        The score is based on the published_date of the most semantically similar PUBLISHED post.

        Args:
            target_idea: The idea to score
            all_published_content: All published content to compare against
            max_age_days: Maximum age in days for scoring (older = 0 score)

        Returns:
            Time score between 0 and 100
        """
        keyword = target_idea.get('keyword', 'Unknown')
        
        if not all_published_content:
            if self.debug_mode:
                print(f"⏰ TIME SCORE DEBUG [{keyword}]: No published content - returning max score 100.0")
            return 100.0  # Maximum freshness if no published content exists

        try:
            if self.debug_mode:
                print(f"⏰ TIME SCORE DEBUG [{keyword}]: Comparing against {len(all_published_content)} published posts")
            
            # Find the most similar published post
            most_similar_post = self._find_most_similar_published_post(target_idea, all_published_content)

            if not most_similar_post or not most_similar_post.get('published_date'):
                if self.debug_mode:
                    print(f"⏰ TIME SCORE DEBUG [{keyword}]: No similar published content found - returning max score 100.0")
                return 100.0  # Maximum freshness if no similar published content

            # Calculate time score based on the published date of the most similar post
            published_date_str = most_similar_post['published_date']
            published_date = datetime.fromisoformat(published_date_str.replace('Z', '+00:00'))
            now = datetime.now(published_date.tzinfo) if published_date.tzinfo else datetime.now()
            age_days = (now - published_date).total_seconds() / (24 * 3600)

            if self.debug_mode:
                similar_keyword = most_similar_post.get('keyword', 'Unknown')
                print(f"⏰ TIME SCORE DEBUG [{keyword}]: Most similar post '{similar_keyword}' published {age_days:.1f} days ago")

            if age_days >= max_age_days:
                if self.debug_mode:
                    print(f"⏰ TIME SCORE DEBUG [{keyword}]: Age {age_days:.1f} >= max {max_age_days} days - returning 0.0")
                return 0.0

            # Linear decay from 100 to 0 over max_age_days
            score = 100 * (1 - age_days / max_age_days)
            final_score = max(0.0, min(100.0, score))
            
            if self.debug_mode:
                print(f"⏰ TIME SCORE DEBUG [{keyword}]: Calculated time score: {final_score:.1f}")
            
            return final_score

        except Exception as e:
            if self.debug_mode:
                print(f"⏰ TIME SCORE DEBUG [{keyword}]: Error calculating time score: {e}")
                print(f"   Full traceback: {traceback.format_exc()}")
            print(f"Error calculating time score: {e}")
            return 50.0  # Default middle score

    def _find_most_similar_published_post(self, target_idea: Dict[str, Any], published_content: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Find the most semantically similar published post to the target idea.

        Args:
            target_idea: The idea to compare
            published_content: List of published content to compare against

        Returns:
            The most similar published post, or None if no comparison possible
        """
        if not self.embedding_model or not target_idea.get('keyword_vector'):
            # Fallback: simple keyword-based similarity
            return self._find_most_similar_published_post_simple(target_idea, published_content)

        try:
            target_vector = deserialize_vector(target_idea['keyword_vector'])
            if target_vector is None:
                return self._find_most_similar_published_post_simple(target_idea, published_content)

            max_similarity = -1
            most_similar_post = None

            for published_post in published_content:
                if not published_post.get('keyword_vector'):
                    continue

                published_vector = deserialize_vector(published_post['keyword_vector'])
                if published_vector is not None:
                    # Calculate cosine similarity
                    similarity = self._calculate_cosine_similarity(target_vector, published_vector)

                    if similarity > max_similarity:
                        max_similarity = similarity
                        most_similar_post = published_post

            return most_similar_post

        except Exception as e:
            print(f"Error finding most similar published post: {e}")
            return self._find_most_similar_published_post_simple(target_idea, published_content)

    def _find_most_similar_published_post_simple(self, target_idea: Dict[str, Any], published_content: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Fallback method to find similar published post using keyword similarity.

        Args:
            target_idea: The idea to compare
            published_content: List of published content to compare against

        Returns:
            The most similar published post based on keyword overlap
        """
        target_keyword = target_idea['keyword'].lower()
        target_words = set(target_keyword.split())

        max_overlap = 0
        most_similar_post = None

        for published_post in published_content:
            published_keyword = published_post['keyword'].lower()
            published_words = set(published_keyword.split())

            if target_words and published_words:
                overlap = len(target_words.intersection(published_words)) / len(target_words.union(published_words))

                if overlap > max_overlap:
                    max_overlap = overlap
                    most_similar_post = published_post

        return most_similar_post
    
    def _calculate_cosine_similarity(self, vector1, vector2) -> float:
        """Calculate cosine similarity between two vectors with numpy fallback."""
        if NUMPY_AVAILABLE:
            # Use numpy for efficient calculation
            return np.dot(vector1, vector2) / (
                np.linalg.norm(vector1) * np.linalg.norm(vector2)
            )
        else:
            # Simple fallback without numpy
            import math
            dot_product = sum(a * b for a, b in zip(vector1, vector2))
            magnitude1 = math.sqrt(sum(a * a for a in vector1))
            magnitude2 = math.sqrt(sum(a * a for a in vector2))
            
            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0
            
            return dot_product / (magnitude1 * magnitude2)
    
    def calculate_uniqueness_score(self, target_idea: Dict[str, Any], 
                                 all_ideas: List[Dict[str, Any]]) -> float:
        """
        Calculate uniqueness score (0-100) based on similarity to existing ideas.
        More unique ideas get higher scores.
        
        Args:
            target_idea: The idea to score
            all_ideas: All ideas to compare against
            
        Returns:
            Uniqueness score between 0 and 100
        """
        keyword = target_idea.get('keyword', 'Unknown')
        
        if not self.embedding_model or not target_idea.get('keyword_vector'):
            if self.debug_mode:
                reason = "No embedding model" if not self.embedding_model else "No keyword vector"
                print(f"🎯 UNIQUENESS DEBUG [{keyword}]: {reason} - using simple keyword fallback")
            # Fallback: simple keyword-based uniqueness
            return self._calculate_simple_uniqueness(target_idea, all_ideas)
        
        try:
            target_vector = deserialize_vector(target_idea['keyword_vector'])
            if target_vector is None:
                if self.debug_mode:
                    print(f"🎯 UNIQUENESS DEBUG [{keyword}]: Failed to deserialize vector - using simple fallback")
                return self._calculate_simple_uniqueness(target_idea, all_ideas)
            
            if self.debug_mode:
                print(f"🎯 UNIQUENESS DEBUG [{keyword}]: Using vector-based similarity comparison")
            
            similarities = []
            comparison_count = 0
            
            for other_idea in all_ideas:
                if other_idea['id'] == target_idea['id']:
                    continue  # Skip self
                
                other_vector = deserialize_vector(other_idea.get('keyword_vector'))
                if other_vector is not None:
                    # Calculate cosine similarity
                    similarity = self._calculate_cosine_similarity(target_vector, other_vector)
                    similarities.append(similarity)
                    comparison_count += 1
                    
                    if self.debug_mode and comparison_count <= 3:  # Log first few comparisons
                        other_keyword = other_idea.get('keyword', 'Unknown')
                        print(f"🎯 UNIQUENESS DEBUG [{keyword}]: vs '{other_keyword}' similarity: {similarity:.3f}")
            
            if not similarities:
                if self.debug_mode:
                    print(f"🎯 UNIQUENESS DEBUG [{keyword}]: No valid vector comparisons - returning max uniqueness 100.0")
                return 100.0  # Completely unique if no comparisons possible
            
            # Convert similarity to uniqueness (higher similarity = lower uniqueness)
            max_similarity = max(similarities)
            uniqueness_score = 100 * (1 - max_similarity)
            final_score = max(0.0, min(100.0, uniqueness_score))
            
            if self.debug_mode:
                print(f"🎯 UNIQUENESS DEBUG [{keyword}]: {comparison_count} comparisons, max similarity: {max_similarity:.3f}, uniqueness: {final_score:.1f}")
            
            return final_score
            
        except Exception as e:
            if self.debug_mode:
                print(f"🎯 UNIQUENESS DEBUG [{keyword}]: Error in vector calculation: {e}")
                print(f"   Full traceback: {traceback.format_exc()}")
            print(f"Error calculating vector-based uniqueness: {e}")
            return self._calculate_simple_uniqueness(target_idea, all_ideas)
    
    def _calculate_simple_uniqueness(self, target_idea: Dict[str, Any], 
                                   all_ideas: List[Dict[str, Any]]) -> float:
        """
        Fallback uniqueness calculation based on keyword similarity.
        
        Args:
            target_idea: The idea to score
            all_ideas: All ideas to compare against
            
        Returns:
            Uniqueness score between 0 and 100
        """
        keyword = target_idea.get('keyword', 'Unknown')
        target_keyword = target_idea['keyword'].lower()
        target_words = set(target_keyword.split())
        
        if self.debug_mode:
            print(f"🔍 SIMPLE UNIQUENESS DEBUG [{keyword}]: Comparing keywords using word overlap")
        
        max_overlap = 0
        comparison_count = 0
        
        for other_idea in all_ideas:
            if other_idea['id'] == target_idea['id']:
                continue
            
            other_keyword = other_idea['keyword'].lower()
            other_words = set(other_keyword.split())
            
            if target_words and other_words:
                overlap = len(target_words.intersection(other_words)) / len(target_words.union(other_words))
                max_overlap = max(max_overlap, overlap)
                comparison_count += 1
                
                if self.debug_mode and comparison_count <= 3:  # Log first few comparisons
                    other_kw_display = other_idea.get('keyword', 'Unknown')
                    print(f"🔍 SIMPLE UNIQUENESS DEBUG [{keyword}]: vs '{other_kw_display}' overlap: {overlap:.3f}")
        
        uniqueness_score = 100 * (1 - max_overlap)
        final_score = max(0.0, min(100.0, uniqueness_score))
        
        if self.debug_mode:
            print(f"🔍 SIMPLE UNIQUENESS DEBUG [{keyword}]: {comparison_count} comparisons, max overlap: {max_overlap:.3f}, uniqueness: {final_score:.1f}")
        
        return final_score
    
    def calculate_angle_bonus(self, proposed_angle: str, max_bonus_points: int = 15, keyword: str = "Unknown") -> float:
        """
        Calculate bonus points based on the quality/specificity of the proposed angle.

        Args:
            proposed_angle: The proposed content angle
            max_bonus_points: Maximum bonus points to award (configurable)
            keyword: Keyword for debug logging

        Returns:
            Bonus points (0 to max_bonus_points)
        """
        if not proposed_angle:
            if self.debug_mode:
                print(f"🎨 ANGLE BONUS DEBUG [{keyword}]: No proposed angle - returning 0.0")
            return 0.0

        if self.debug_mode:
            print(f"🎨 ANGLE BONUS DEBUG [{keyword}]: Analyzing angle: '{proposed_angle[:50]}{'...' if len(proposed_angle) > 50 else ''}'")

        bonus = 0.0
        bonus_reasons = []
        angle_lower = proposed_angle.lower()

        # Bonus for specific, actionable language
        action_words = ['how to', 'guide to', 'step by step', 'complete', 'ultimate', 'comprehensive']
        for word in action_words:
            if word in angle_lower:
                bonus += 3.0
                bonus_reasons.append(f"Action word '{word}' (+3.0)")
                break

        # Bonus for Stuga-aligned terms
        stuga_terms = ['natural', 'sustainable', 'artisan', 'handmade', 'quality', 'traditional', 'crafted']
        for term in stuga_terms:
            if term in angle_lower:
                bonus += 2.0
                bonus_reasons.append(f"Stuga term '{term}' (+2.0)")
                break

        # Bonus for specificity (longer, more detailed angles)
        if len(proposed_angle) > 100:
            bonus += 5.0
            bonus_reasons.append(f"Long angle ({len(proposed_angle)} chars) (+5.0)")
        elif len(proposed_angle) > 50:
            bonus += 2.0
            bonus_reasons.append(f"Medium angle ({len(proposed_angle)} chars) (+2.0)")

        # Bonus for Australian market focus
        if 'australia' in angle_lower or 'aussie' in angle_lower:
            bonus += 3.0
            bonus_reasons.append("Australian focus (+3.0)")

        final_bonus = min(float(max_bonus_points), bonus)  # Cap at configurable max points
        
        if self.debug_mode:
            if bonus_reasons:
                print(f"🎨 ANGLE BONUS DEBUG [{keyword}]: Bonuses: {', '.join(bonus_reasons)}")
            print(f"🎨 ANGLE BONUS DEBUG [{keyword}]: Final angle bonus: {final_bonus:.1f} (capped at {max_bonus_points})")

        return final_bonus
    
    def calculate_freshness_scores(self, pillar_weights: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Calculate freshness scores for all NEW and ON_HOLD content.

        Args:
            pillar_weights: Dictionary mapping craft names to weight multipliers

        Returns:
            List of content ideas with calculated scores, sorted by score descending
        """
        # Import config_manager here to avoid circular imports
        from core import config_manager

        # Get configurable scoring weights
        time_weight = float(config_manager.get_pipeline_setting('time_score_weight') or 0.5)
        uniqueness_weight = float(config_manager.get_pipeline_setting('uniqueness_score_weight') or 0.5)
        max_angle_bonus = int(config_manager.get_pipeline_setting('angle_bonus_max_points') or 15)

        # Get all content that needs scoring
        content_to_score = self.db.get_content_by_statuses(['NEW', 'ON_HOLD'])

        if not content_to_score:
            print("No content found to score")
            return []

        # Get all published content for time score calculation
        published_content = self.db.get_content_by_status('PUBLISHED')

        print(f"Calculating freshness scores for {len(content_to_score)} content ideas...")
        print(f"Comparing against {len(published_content)} published posts for time scoring...")
        print(f"Using weights: Time={time_weight:.1f}, Uniqueness={uniqueness_weight:.1f}, Max Angle Bonus={max_angle_bonus}")

        scored_content = []

        for idea in content_to_score:
            keyword = idea.get('keyword', 'Unknown')
            idea_id = idea.get('id', 'Unknown')
            
            try:
                if self.debug_mode:
                    print(f"\n🔍 === SCORING DEBUG: '{keyword}' (ID: {idea_id}) ===")
                    
                    # Check for vector existence
                    has_vector = bool(idea.get('keyword_vector'))
                    print(f"🧠 VECTOR STATUS: {'Available' if has_vector else 'Missing'}")
                    
                    if has_vector:
                        try:
                            vector = deserialize_vector(idea['keyword_vector'])
                            vector_valid = vector is not None
                            print(f"🧠 VECTOR DESERIALIZE: {'Success' if vector_valid else 'Failed'}")
                        except Exception as ve:
                            print(f"🧠 VECTOR DESERIALIZE: Error - {ve}")
                
                # Calculate component scores
                time_score = self.calculate_time_score(idea, published_content)
                uniqueness_score = self.calculate_uniqueness_score(idea, content_to_score)
                angle_bonus = self.calculate_angle_bonus(idea.get('proposed_angle', ''), max_angle_bonus, keyword)

                # Get pillar weight
                craft = idea.get('craft', '')
                pillar_weight = pillar_weights.get(craft, 1.0)
                
                if self.debug_mode:
                    print(f"⚖️ PILLAR WEIGHT DEBUG [{keyword}]: Craft '{craft}' has weight {pillar_weight}")

                # Calculate final score using configurable weights
                weighted_core = (time_score * time_weight) + (uniqueness_score * uniqueness_weight)
                multiplied_core = weighted_core * pillar_weight
                final_score = multiplied_core + angle_bonus
                
                if self.debug_mode:
                    print(f"🏆 FINAL SCORE CALCULATION [{keyword}]:")
                    print(f"   Core = (Time:{time_score:.1f} × {time_weight}) + (Unique:{uniqueness_score:.1f} × {uniqueness_weight}) = {weighted_core:.1f}")
                    print(f"   Multiplied = {weighted_core:.1f} × {pillar_weight} = {multiplied_core:.1f}")
                    print(f"   Final = {multiplied_core:.1f} + {angle_bonus:.1f} = {final_score:.1f}")

                # Validate final score
                if final_score == 0.0:
                    print(f"🚨 WARNING: '{keyword}' received final score of 0.0!")
                    print(f"   Components: Time={time_score:.1f}, Unique={uniqueness_score:.1f}, Weight={pillar_weight}, Bonus={angle_bonus:.1f}")

                # Ensure final_score is a proper float before updating database
                try:
                    final_score = float(final_score)
                    if str(final_score).lower() in ('nan', 'inf', '-inf'):
                        final_score = 0.0
                except (ValueError, TypeError):
                    print(f"❌ PLANNER: Invalid final_score {repr(final_score)} for '{keyword}', using 0.0")
                    final_score = 0.0
                
                # Update the score in the database
                self.db.update_freshness_score(idea['id'], final_score)

                # Add to results
                scored_idea = idea.copy()
                scored_idea['freshness_score'] = final_score
                scored_idea['time_score'] = time_score
                scored_idea['uniqueness_score'] = uniqueness_score
                scored_idea['angle_bonus'] = angle_bonus
                scored_idea['pillar_weight'] = pillar_weight

                scored_content.append(scored_idea)

                print(f"Scored '{keyword}': Final={final_score:.1f} "
                      f"(Time={time_score:.1f}, Unique={uniqueness_score:.1f}, "
                      f"Weight={pillar_weight:.1f}, Bonus={angle_bonus:.1f})")

            except Exception as e:
                print(f"Error scoring idea '{keyword}' (ID: {idea_id}): {e}")
                if self.debug_mode:
                    print(f"   Full traceback: {traceback.format_exc()}")
                continue

        # Sort by freshness score descending
        def safe_sort_key(x):
            score = x.get('freshness_score', 0)
            # Handle bytes objects that might be in the database
            if isinstance(score, bytes):
                return 0.0
            try:
                return float(score)
            except (ValueError, TypeError):
                return 0.0
        
        scored_content.sort(key=safe_sort_key, reverse=True)

        print(f"Scoring complete. {len(scored_content)} ideas scored.")
        
        # Store debug information for GUI display
        self.last_scoring_debug = {
            'embedding_status': self.embedding_status,
            'content_count': len(content_to_score),
            'published_count': len(published_content),
            'settings': {
                'time_weight': time_weight,
                'uniqueness_weight': uniqueness_weight,
                'max_angle_bonus': max_angle_bonus
            },
            'pillar_weights': pillar_weights.copy(),
            'zero_scores': [idea['keyword'] for idea in scored_content if idea.get('freshness_score', 0) == 0.0]
        }
        
        # Debug summary
        if self.debug_mode and scored_content:
            print(f"\n📊 === SCORING SUMMARY ===")
            print(f"Embedding Status: {self.embedding_status}")
            print(f"Content Scored: {len(scored_content)}")
            print(f"Published Content for Time Comparison: {len(published_content)}")
            print(f"Settings: Time Weight={time_weight}, Uniqueness Weight={uniqueness_weight}, Max Angle Bonus={max_angle_bonus}")
            
            if self.last_scoring_debug['zero_scores']:
                print(f"⚠️  Items with 0.0 scores: {', '.join(self.last_scoring_debug['zero_scores'])}")
            
            # Show top 3 and bottom 3 scores
            print(f"\nTop 3 Scores:")
            for i, idea in enumerate(scored_content[:3]):
                score = idea.get('freshness_score', 0)
                print(f"  {i+1}. '{idea.get('keyword', 'Unknown')}': {score:.1f}")
            
            if len(scored_content) > 3:
                print(f"\nBottom 3 Scores:")
                for i, idea in enumerate(scored_content[-3:]):
                    score = idea.get('freshness_score', 0)
                    print(f"  {len(scored_content)-2+i}. '{idea.get('keyword', 'Unknown')}': {score:.1f}")
        
        return scored_content
    
    def select_next_job(self, pillar_weights: Dict[str, float], 
                       freshness_threshold: float = 70.0) -> Optional[Dict[str, Any]]:
        """
        Select the next content idea to work on based on freshness scoring.
        
        Args:
            pillar_weights: Dictionary mapping craft names to weight multipliers
            freshness_threshold: Minimum score threshold for selection
            
        Returns:
            The selected content idea dictionary, or None if no suitable content found
        """
        print(f"Selecting next job with threshold {freshness_threshold}...")
        
        # Calculate scores for all eligible content
        scored_content = self.calculate_freshness_scores(pillar_weights)
        
        if not scored_content:
            print("No content available for scoring")
            return None
        
        # Find the highest scoring content that meets the threshold
        for idea in scored_content:
            if idea['freshness_score'] >= freshness_threshold:
                # Mark as PLANNED
                success = self.db.update_content_status(idea['id'], 'PLANNED')
                if success:
                    print(f"Selected job: '{idea['keyword']}' (Score: {idea['freshness_score']:.1f})")
                    return idea
                else:
                    print(f"Failed to update status for idea {idea['id']}, trying next...")
                    continue
        
        print(f"No content meets the freshness threshold of {freshness_threshold}")
        print("Top 3 candidates:")
        for i, idea in enumerate(scored_content[:3]):
            print(f"  {i+1}. '{idea['keyword']}' - Score: {idea['freshness_score']:.1f}")
        
        return None
    
    def get_scoring_debug_info(self) -> Dict[str, Any]:
        """Get the last scoring debug information for display in GUI."""
        return self.last_scoring_debug.copy() if self.last_scoring_debug else {}
    
    def get_queue_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current content queue.
        
        Returns:
            Dictionary with queue statistics and top candidates
        """
        stats = self.db.get_content_stats()
        
        # Get top candidates by current freshness score
        all_content = self.db.get_content_by_statuses(['NEW', 'ON_HOLD'])
        
        def safe_sort_key(x):
            score = x.get('freshness_score', 0)
            if isinstance(score, bytes):
                return 0.0
            try:
                return float(score)
            except (ValueError, TypeError):
                return 0.0
        
        top_candidates = sorted(all_content, key=safe_sort_key, reverse=True)[:5]
        
        return {
            'stats': stats,
            'total_in_queue': stats.get('NEW', 0) + stats.get('ON_HOLD', 0),
            'top_candidates': top_candidates
        }


# Convenience functions
def plan_next_content(pillar_weights: Dict[str, float], 
                     freshness_threshold: float = 70.0) -> Optional[Dict[str, Any]]:
    """
    Convenience function to select the next content to work on.
    
    Args:
        pillar_weights: Dictionary mapping craft names to weight multipliers
        freshness_threshold: Minimum score threshold for selection
        
    Returns:
        The selected content idea dictionary, or None if no suitable content found
    """
    planner = ContentPlanner()
    return planner.select_next_job(pillar_weights, freshness_threshold)


def refresh_all_scores(pillar_weights: Dict[str, float]) -> List[Dict[str, Any]]:
    """
    Convenience function to refresh freshness scores for all content.

    Args:
        pillar_weights: Dictionary mapping craft names to weight multipliers

    Returns:
        List of all scored content ideas
    """
    planner = ContentPlanner()
    return planner.calculate_freshness_scores(pillar_weights)


# Example usage and testing
if __name__ == '__main__':
    print("Testing Content Planner...")
    
    # Test with sample pillar weights
    test_weights = {
        'grooming': 1.2,
        'leather goods': 1.0,
        'fragrance': 0.8
    }
    
    planner = ContentPlanner()
    
    # Test queue summary
    summary = planner.get_queue_summary()
    print("Queue Summary:", summary)
    
    # Test scoring (requires content in database)
    try:
        scored_content = planner.calculate_freshness_scores(test_weights)
        print(f"\nScored {len(scored_content)} content ideas")
        
        if scored_content:
            print("Top 3 scored ideas:")
            for i, idea in enumerate(scored_content[:3]):
                print(f"  {i+1}. '{idea['keyword']}' - Score: {idea['freshness_score']:.1f}")
        
        # Test job selection
        next_job = planner.select_next_job(test_weights, freshness_threshold=50.0)
        if next_job:
            print(f"\nSelected next job: '{next_job['keyword']}'")
        else:
            print("\nNo job selected (threshold not met or no content available)")
            
    except Exception as e:
        print(f"Could not test scoring: {e}")
        print("Make sure there is content in the database (run idea_generator.py first)")
    
    print("\nContent Planner testing complete!")
