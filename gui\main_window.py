"""
Main Window - Content Strategist Dashboard

This is the main dashboard for the Content Strategist system.
It features four tabs:
1. Dashboard - Main control panel with "Run Next Job" button and LLM selection
2. Content Plan - Database view with TreeView and management buttons
3. Idea Generator - Interface for generating new content ideas with LLM selection
4. Log - Real-time application logging
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
import struct

from gui.settings_window import SettingsWindow
from core import config_manager
from core.llm_interface import AVAILABLE_MODELS
from database import ContentDatabase
from idea_generator import IdeaGenerator
from planner import ContentPlanner
from worker import ContentWorker


def decode_freshness_score(score, item_name="Unknown"):
    """
    Utility function to safely decode freshness scores that might be stored as bytes.
    
    Args:
        score: The score value (could be bytes, float, string, or None)
        item_name: Name of the item for debugging (optional)
    
    Returns:
        float: Properly decoded score value
    """
    if score is None:
        return 0.0
    
    if isinstance(score, bytes):
        try:
            if len(score) == 4:
                # Decode 32-bit IEEE 754 float
                decoded = struct.unpack('f', score)[0]
                print(f"✅ DECODE [{item_name}]: Decoded {repr(score)} to {decoded:.2f}")
                return float(decoded)
            elif len(score) == 8:
                # Decode 64-bit IEEE 754 double
                decoded = struct.unpack('d', score)[0]
                print(f"✅ DECODE [{item_name}]: Decoded 64-bit {repr(score)} to {decoded:.2f}")
                return float(decoded)
            else:
                print(f"⚠️ DECODE [{item_name}]: Invalid bytes length {len(score)}, using 0.0")
                return 0.0
        except (struct.error, ValueError) as e:
            print(f"❌ DECODE [{item_name}]: Error decoding bytes {repr(score)}: {e}")
            return 0.0
    
    # Handle string or numeric values
    try:
        return float(score)
    except (ValueError, TypeError):
        print(f"⚠️ DECODE [{item_name}]: Could not convert {repr(score)} to float, using 0.0")
        return 0.0


def safe_sort_key_for_freshness(item_dict):
    """
    Safe sort key function for freshness scores that handles bytes properly.
    
    Args:
        item_dict: Dictionary containing content data
    
    Returns:
        float: Properly decoded score for sorting
    """
    score = item_dict.get('freshness_score', 0.0)
    keyword = item_dict.get('keyword', 'Unknown')
    return decode_freshness_score(score, keyword)


class MainWindow:
    """
    The main dashboard for the Content Strategist application.
    """

    def __init__(self, master):
        self.master = master
        master.title("Content Strategist Dashboard")
        master.geometry("1400x900")
        master.resizable(True, True)

        # Initialize backend components
        self.db = ContentDatabase()
        self.idea_generator = IdeaGenerator()
        self.planner = ContentPlanner(debug_mode=True)  # Enable debugging for freshness scoring
        self.worker = ContentWorker()

        # Initialize variables
        self.is_running = False
        self.current_thread = None
        self.stop_requested = False  # Flag to request stopping operations
        self.debug_mode = True  # Enable GUI debug output for freshness score display

        # LLM selection variables
        self.writing_llm_provider_var = tk.StringVar()
        self.writing_llm_model_var = tk.StringVar()
        self.idea_gen_llm_provider_var = tk.StringVar()
        self.idea_gen_llm_model_var = tk.StringVar()

        # Create main frame
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create the tabbed interface
        self.create_tabbed_interface(main_frame)

        # Create menu bar
        self.create_menu_bar()

        # Bind the close event
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize LLM dropdowns after all tabs are created
        self.populate_llm_dropdowns()

        # Initial data load
        self.refresh_content_plan()
        self.update_dashboard_status()
        self.log_message("Content Strategist Dashboard initialized successfully!")
    
    def create_tabbed_interface(self, parent):
        """Create the main tabbed interface."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.create_dashboard_tab()
        self.create_content_plan_tab()
        self.create_idea_generator_tab()
        self.create_log_tab()
    
    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Settings", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Database Statistics", command=self.show_database_stats)
        tools_menu.add_command(label="Refresh All Scores", command=self.refresh_all_scores)
        tools_menu.add_separator()
        tools_menu.add_command(label="🔍 Scoring Debug Info", command=self.show_scoring_debug)
        tools_menu.add_command(label="🧠 Embedding Status", command=self.show_embedding_status)
        tools_menu.add_command(label="🔄 Force Refresh GUI", command=self.force_refresh_gui)
        tools_menu.add_separator()
        tools_menu.add_command(label="Open Database File", command=self.open_database_file)
        tools_menu.add_command(label="Export to CSV", command=self.export_to_csv)
        tools_menu.add_command(label="Database Cleanup", command=self.open_cleanup_dialog)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_dashboard_tab(self):
        """Create the Dashboard tab with improved layout."""
        dashboard_frame = ttk.Frame(self.notebook, padding="15")
        self.notebook.add(dashboard_frame, text="Dashboard")

        # Title
        title_label = ttk.Label(dashboard_frame, text="Content Strategist Dashboard",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 15))

        # Top row: Planning and Execution side by side
        top_row = ttk.Frame(dashboard_frame)
        top_row.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Left side: Job Planning
        planning_frame = ttk.LabelFrame(top_row, text="📋 Job Planning", padding="12")
        planning_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # Plan job controls
        plan_controls = ttk.Frame(planning_frame)
        plan_controls.pack(fill=tk.X, pady=(0, 10))

        self.plan_job_button = ttk.Button(plan_controls, text="🔎 Plan Next Job",
                                        command=self.plan_next_job)
        self.plan_job_button.pack(side=tk.LEFT, padx=(0, 8))

        self.stop_button = ttk.Button(plan_controls, text="🛑 Stop",
                                     command=self.stop_operations, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for planning
        self.job_progress = ttk.Progressbar(plan_controls, mode='indeterminate', length=150)

        # Next job info (expandable)
        ttk.Label(planning_frame, text="Next Job Candidate:", font="-weight bold").pack(anchor=tk.W, pady=(8, 2))
        self.next_job_info = tk.Text(planning_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        self.next_job_info.pack(fill=tk.BOTH, expand=True)

        # Right side: Job Execution
        self.execution_frame = ttk.LabelFrame(top_row, text="🚀 Job Execution", padding="12")
        self.execution_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(8, 0))

        # Create execution controls
        self.create_execution_controls()

        # Middle row: Manual Entry (horizontal layout)
        manual_frame = ttk.LabelFrame(dashboard_frame, text="➕ Quick Add Content", padding="12")
        manual_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.create_manual_entry_section(manual_frame)

        # Bottom row: Statistics (expandable)
        stats_frame = ttk.LabelFrame(dashboard_frame, text="📊 Database Status", padding="10")
        stats_frame.pack(fill=tk.BOTH, expand=True)

        self.stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True)

        # Initialize execution controls to disabled state
        self.reset_execution_controls()
        self.populate_manual_entry_dropdowns()
        self.update_dashboard_status()

    def create_manual_entry_section(self, parent):
        """Create the manual entry section with improved responsive layout."""
        # Main grid
        entry_grid = ttk.Frame(parent)
        entry_grid.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        entry_grid.columnconfigure(1, weight=2)  # Keyword entry gets more space
        entry_grid.columnconfigure(3, weight=1)  # Craft combo expandable
        entry_grid.columnconfigure(5, weight=1)  # Pillar combo expandable

        # Row 1: Keyword, Craft, and Pillar
        ttk.Label(entry_grid, text="Keyword:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=5)
        self.manual_keyword_var = tk.StringVar()
        ttk.Entry(entry_grid, textvariable=self.manual_keyword_var).grid(row=0, column=1, sticky="ew", padx=(0, 15), pady=5)

        ttk.Label(entry_grid, text="Craft:").grid(row=0, column=2, sticky="w", padx=(0, 8), pady=5)
        self.manual_craft_var = tk.StringVar()
        self.manual_craft_combo = ttk.Combobox(entry_grid, textvariable=self.manual_craft_var, state="readonly")
        self.manual_craft_combo.grid(row=0, column=3, sticky="ew", padx=(0, 15), pady=5)
        self.manual_craft_combo.bind('<<ComboboxSelected>>', self.on_manual_craft_changed)

        ttk.Label(entry_grid, text="Pillar:").grid(row=0, column=4, sticky="w", padx=(0, 8), pady=5)
        self.manual_pillar_var = tk.StringVar()
        self.manual_pillar_combo = ttk.Combobox(entry_grid, textvariable=self.manual_pillar_var, state="readonly")
        self.manual_pillar_combo.grid(row=0, column=5, sticky="ew", pady=5)

        # Row 2: Angle and Add button
        ttk.Label(entry_grid, text="Angle:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=5)
        self.manual_angle_var = tk.StringVar()
        angle_entry = ttk.Entry(entry_grid, textvariable=self.manual_angle_var)
        angle_entry.grid(row=1, column=1, columnspan=4, sticky="ew", padx=(0, 15), pady=5)

        ttk.Button(entry_grid, text="➕ Add", command=self.add_manual_content_idea).grid(row=1, column=5, sticky="ew", pady=5)

    def create_execution_controls(self):
        """Create the execution controls section with compact layout."""
        # Initialize variables for execution controls
        self.current_planned_job = None
        self.last_job_execution_result = None  # Store last job output for View/Save functionality
        self.run_data_collection_var = tk.BooleanVar(value=True)
        self.run_blog_writing_var = tk.BooleanVar(value=True)
        self.run_publish_var = tk.BooleanVar(value=True)
        
        # Multiple data source checkboxes (replace single dropdown)
        self.use_serpapi_var = tk.BooleanVar(value=True)
        self.use_alsoasked_var = tk.BooleanVar(value=False)
        self.use_llm_suggestion_var = tk.BooleanVar(value=False)
        
        # Publishing controls
        self.publish_platform_var = tk.StringVar(value="Shopify")
        self.publish_as_draft_var = tk.BooleanVar(value=True)
        
        # Instructions
        instruction_label = ttk.Label(self.execution_frame,
                                     text="Plan a job first to enable execution controls.",
                                     font=("Arial", 9), foreground="gray")
        instruction_label.pack(pady=(0, 8))
        
        # Planned job details (expandable)
        self.planned_job_text = tk.Text(self.execution_frame, height=4, wrap=tk.WORD, state=tk.DISABLED)
        self.planned_job_text.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        # Execution options in a responsive grid
        options_frame = ttk.Frame(self.execution_frame)
        options_frame.pack(fill=tk.X, pady=(0, 8))

        # Configure grid weights for proper expansion
        options_frame.columnconfigure(1, weight=1)  # Settings column expands

        # Column 1: Steps
        steps_frame = ttk.LabelFrame(options_frame, text="Steps", padding="8")
        steps_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 8))

        ttk.Checkbutton(steps_frame, text="SERP", variable=self.run_data_collection_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Write", variable=self.run_blog_writing_var).pack(anchor=tk.W)
        ttk.Checkbutton(steps_frame, text="Publish", variable=self.run_publish_var).pack(anchor=tk.W)

        # Column 2: Source & Platform
        settings_frame = ttk.LabelFrame(options_frame, text="Settings", padding="8")
        settings_frame.grid(row=0, column=1, sticky="nsew")

        # Configure settings grid for proper expansion
        settings_frame.columnconfigure(1, weight=1)  # Dropdown column expands

        # Data sources section
        ttk.Label(settings_frame, text="Data Sources:").grid(row=0, column=0, sticky="w", padx=(0, 8), pady=3)
        sources_subframe = ttk.Frame(settings_frame)
        sources_subframe.grid(row=0, column=1, sticky="w", pady=3)
        
        ttk.Checkbutton(sources_subframe, text="SerpApi", variable=self.use_serpapi_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="AlsoAsked", variable=self.use_alsoasked_var).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(sources_subframe, text="LLM", variable=self.use_llm_suggestion_var).pack(side=tk.LEFT)

        # Platform row
        ttk.Label(settings_frame, text="Platform:").grid(row=1, column=0, sticky="w", padx=(0, 8), pady=3)
        platform_combo = ttk.Combobox(settings_frame, textvariable=self.publish_platform_var, state="readonly")
        platform_combo['values'] = ["Shopify", "WordPress"]
        platform_combo.grid(row=1, column=1, sticky="ew", pady=3)
        
        # Draft checkbox row
        ttk.Checkbutton(settings_frame, text="Publish as Draft", variable=self.publish_as_draft_var).grid(row=2, column=0, columnspan=2, sticky="w", pady=3)

        # LLM selection row - using a sub-frame for provider and model
        ttk.Label(settings_frame, text="LLM:").grid(row=3, column=0, sticky="w", padx=(0, 8), pady=3)
        llm_frame = ttk.Frame(settings_frame)
        llm_frame.grid(row=3, column=1, sticky="ew", pady=3)
        llm_frame.columnconfigure(1, weight=1)  # Model combo expands more

        self.writing_provider_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_provider_var, state="readonly", width=10)
        self.writing_provider_combo.grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.writing_provider_combo.bind('<<ComboboxSelected>>', self.on_writing_provider_changed)

        self.writing_model_combo = ttk.Combobox(llm_frame, textvariable=self.writing_llm_model_var, state="readonly")
        self.writing_model_combo.grid(row=0, column=1, sticky="ew")

        # Execute button
        execute_frame = ttk.Frame(self.execution_frame)
        execute_frame.pack(pady=8)

        self.execute_job_button = ttk.Button(execute_frame, text="🚀 Execute Job",
                                           command=self.execute_planned_job, state=tk.DISABLED)
        self.execute_job_button.pack(side=tk.LEFT, padx=(0, 8))

        # Progress bar for execution (initially hidden)
        self.execution_progress = ttk.Progressbar(execute_frame, mode='indeterminate', length=150)

    def create_content_plan_tab(self):
        """Create the Content Plan tab - database view and management."""
        content_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(content_frame, text="Content Plan")

        # Title and description
        title_label = ttk.Label(content_frame, text="Content Plan",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))

        desc_label = ttk.Label(content_frame,
                              text="View and manage all content ideas. Green highlight = Next Job Candidate. Right-click for more options.",
                              font=("Arial", 10), foreground="gray")
        desc_label.pack(pady=(0, 15))

        # Search functionality
        search_frame = ttk.Frame(content_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="Search Content Plan:").pack(side=tk.LEFT, padx=(0, 10))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(search_frame, text="Clear Search", command=self.clear_search).pack(side=tk.LEFT, padx=(0, 20))

        # Management buttons
        ttk.Button(search_frame, text="🔄 Refresh Table", command=self.refresh_content_plan).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_frame, text="📊 Auto-Calculate Scores", command=self.auto_calculate_scores).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="⭐ Prioritize", command=self.prioritize_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="❌ Veto Idea", command=self.veto_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="🗑️ Delete", command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="🔄 Refresh All Scores", command=self.refresh_all_scores).pack(side=tk.LEFT, padx=5)
        ttk.Button(search_frame, text="🧹 Cleanup DB", command=self.open_cleanup_dialog).pack(side=tk.LEFT, padx=5)

        # Main content area with resizable panes
        content_paned = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
        content_paned.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # TreeView frame
        tree_frame = ttk.Frame(content_paned)
        content_paned.add(tree_frame, weight=3)  # Give tree view more initial space

        # Create TreeView with columns
        columns = ('Status', 'Freshness', 'Keyword', 'Pillar', 'Proposed_Angle')
        self.content_tree = ttk.Treeview(tree_frame, columns=columns, show='tree headings')

        # Configure column headings and widths with sorting
        self.content_tree.heading('#0', text='ID ↕', command=lambda: self.sort_content_tree('#0'))
        self.content_tree.column('#0', width=50, minwidth=50)

        for col in columns:
            self.content_tree.heading(col, text=f'{col} ↕', command=lambda c=col: self.sort_content_tree(c))
            if col == 'Keyword':
                self.content_tree.column(col, width=180, minwidth=120)
            elif col == 'Proposed_Angle':
                self.content_tree.column(col, width=250, minwidth=150)
            elif col == 'Freshness':
                self.content_tree.column(col, width=80, minwidth=60)
            else:
                self.content_tree.column(col, width=80, minwidth=60)

        # Add scrollbar for TreeView
        tree_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.content_tree.yview)
        self.content_tree.configure(yscrollcommand=tree_scrollbar.set)

        # Pack TreeView and scrollbar
        self.content_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure tags for visual highlighting
        self.content_tree.tag_configure('next_candidate', background='lightgreen')
        self.content_tree.tag_configure('planned', background='lightblue')
        self.content_tree.tag_configure('high_priority', background='lightyellow')
        self.content_tree.tag_configure('rejected', background='lightcoral')

        # Bind selection event
        self.content_tree.bind('<<TreeviewSelect>>', self.on_content_selection_changed)
        
        # Bind right-click context menu
        self.content_tree.bind('<Button-3>', self.show_context_menu)  # Right-click
        self.content_tree.bind('<Control-Button-1>', self.show_context_menu)  # Ctrl+Click for Mac

        # Detail pane frame
        detail_frame = ttk.LabelFrame(content_paned, text="Content Details", padding="8")
        content_paned.add(detail_frame, weight=1)  # Less initial space for detail pane

        self.detail_text = tk.Text(detail_frame, width=30, wrap=tk.WORD, state=tk.DISABLED)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)

        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Status bar
        self.content_status_var = tk.StringVar(value="Ready")
        ttk.Label(content_frame, textvariable=self.content_status_var).pack(pady=(10, 0))

        # Initialize sorting state
        self.sort_column = None
        self.sort_reverse = False
        self.current_content_data = []
        self.next_job_candidate_id = None  # Track the next job candidate
        
        # Create context menu
        self.create_context_menu()
    
    def create_context_menu(self):
        """Create the right-click context menu for the content tree."""
        self.context_menu = tk.Menu(self.master, tearoff=0)
        self.context_menu.add_command(label="🚀 Plan This Job", command=self.plan_selected_job)
        self.context_menu.add_command(label="⭐ Set High Priority (999)", command=self.prioritize_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📝 Set Custom Score...", command=self.set_custom_score)
        self.context_menu.add_command(label="🔄 Move to Top", command=self.move_to_top)
        self.context_menu.add_command(label="⬇️ Move to Bottom", command=self.move_to_bottom)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="❌ Veto/Reject", command=self.veto_selected)
        self.context_menu.add_command(label="🗑️ Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 Copy Keyword", command=self.copy_keyword)
        self.context_menu.add_command(label="📄 View Details", command=self.view_details)
    
    def show_context_menu(self, event):
        """Show the context menu at the cursor position."""
        try:
            # Select the item under the cursor
            item = self.content_tree.identify_row(event.y)
            if item:
                self.content_tree.selection_set(item)
                self.content_tree.focus(item)
                
                # Show the context menu
                self.context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.log_message(f"Error showing context menu: {e}", "error")
    
    def plan_selected_job(self):
        """Plan the selected job directly (bypass threshold)."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to plan.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            
            # Get the content details
            content = self.db.get_content_by_id(content_id)
            if not content:
                messagebox.showerror("Error", "Content not found in database.")
                return
            
            # 1. Database Operation: Mark as planned and store
            self.db.update_content_status(content_id, 'PLANNED')
            self.current_planned_job = content
            
            # 2. Log the Action
            self.log_message(f"✅ Manually planned job: '{content['keyword']}' (ID: {content_id})", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new auto-candidate)
            # Note: This identifies the NEXT auto-candidate (different from the manually planned job)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.refresh_content_plan()
            
            # 5. Update Dashboard Statistics and show execution controls
            self.show_execution_controls()
            self.update_dashboard_status()
            
            # Switch to Dashboard tab to show execution controls
            self.notebook.select(0)  # Switch to Dashboard tab
            
        except Exception as e:
            self.log_message(f"Error planning selected job: {e}", "error")
    
    def set_custom_score(self):
        """Set a custom freshness score for the selected content."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to set score.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            keyword = item['values'][2]  # Keyword is at index 2
            
            # Create a simple dialog for score input
            score_window = tk.Toplevel(self.master)
            score_window.title("Set Custom Score")
            score_window.geometry("300x150")
            score_window.resizable(False, False)
            score_window.transient(self.master)
            score_window.grab_set()
            
            # Center the window
            score_window.update_idletasks()
            x = (score_window.winfo_screenwidth() // 2) - (300 // 2)
            y = (score_window.winfo_screenheight() // 2) - (150 // 2)
            score_window.geometry(f"300x150+{x}+{y}")
            
            main_frame = ttk.Frame(score_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(main_frame, text=f"Set score for:", font=("Arial", 10, "bold")).pack()
            ttk.Label(main_frame, text=f"'{keyword}'", wraplength=250).pack(pady=(0, 10))
            
            score_frame = ttk.Frame(main_frame)
            score_frame.pack(pady=5)
            
            ttk.Label(score_frame, text="Score (0-999):").pack(side=tk.LEFT, padx=(0, 10))
            score_var = tk.StringVar(value="100")
            score_entry = ttk.Entry(score_frame, textvariable=score_var, width=10)
            score_entry.pack(side=tk.LEFT)
            score_entry.focus()
            score_entry.select_range(0, tk.END)
            
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=15)
            
            def apply_score():
                try:
                    score = float(score_var.get())
                    if 0 <= score <= 999:
                        self.db.update_freshness_score(content_id, score)
                        self.log_message(f"📊 Set custom score {score} for '{keyword}' (ID: {content_id})", "success")
                        
                        # Update next job candidate FIRST (recalculate scores)
                        self.update_next_job_info()
                        
                        # Then refresh UI with the updated candidate
                        self.refresh_content_plan()
                        self.update_dashboard_status()
                        score_window.destroy()
                    else:
                        messagebox.showerror("Invalid Score", "Score must be between 0 and 999.", parent=score_window)
                except ValueError:
                    messagebox.showerror("Invalid Input", "Please enter a valid number.", parent=score_window)
            
            ttk.Button(button_frame, text="Apply", command=apply_score).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="Cancel", command=score_window.destroy).pack(side=tk.LEFT)
            
            # Bind Enter key to apply
            score_window.bind('<Return>', lambda e: apply_score())
            
        except Exception as e:
            self.log_message(f"Error setting custom score: {e}", "error")
    
    def move_to_top(self):
        """Move selected content to top of queue."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to move.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            
            # 1. Database Operation: Set score higher than any existing score
            all_content = self.db.get_all_content()
            max_score = 0
            for content in all_content:
                try:
                    score = decode_freshness_score(content.get('freshness_score', 0))
                    max_score = max(max_score, score)
                except (ValueError, TypeError):
                    pass
            
            new_score = max_score + 10
            self.db.update_freshness_score(content_id, new_score)

            # 2. Log the Action
            self.log_message(f"⬆️ Moved content ID {content_id} to top (Score: {new_score})", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()
            
        except Exception as e:
            self.log_message(f"Error moving to top: {e}", "error")
    
    def move_to_bottom(self):
        """Move selected content to bottom of queue."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to move.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            
            # 1. Database Operation: Set score to 1 (very low priority)
            self.db.update_freshness_score(content_id, 1.0)
            
            # 2. Log the Action
            self.log_message(f"⬇️ Moved content ID {content_id} to bottom (Score: 1.0)", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()
            
        except Exception as e:
            self.log_message(f"Error moving to bottom: {e}", "error")
    
    def copy_keyword(self):
        """Copy the selected keyword to clipboard."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to copy.")
            return

        try:
            item = self.content_tree.item(selected[0])
            keyword = item['values'][2]  # Keyword is at index 2
            
            # Copy to clipboard
            self.master.clipboard_clear()
            self.master.clipboard_append(keyword)
            
            self.log_message(f"📋 Copied keyword '{keyword}' to clipboard", "info")
            
        except Exception as e:
            self.log_message(f"Error copying keyword: {e}", "error")
    
    def view_details(self):
        """View detailed information about the selected content."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to view details.")
            return

        try:
            # This will automatically update the detail pane
            self.on_content_selection_changed(None)
            
        except Exception as e:
            self.log_message(f"Error viewing details: {e}", "error")

    def create_idea_generator_tab(self):
        """Create the Idea Generator tab."""
        idea_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(idea_frame, text="Idea Generator")

        # Controls frame
        controls_frame = ttk.LabelFrame(idea_frame, text="Generation Settings", padding="10")
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Controls grid
        controls_grid = ttk.Frame(controls_frame)
        controls_grid.pack(fill=tk.X)

        # Number of ideas
        ttk.Label(controls_grid, text="Number of ideas:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.num_ideas_var = tk.IntVar(value=5)
        ttk.Spinbox(controls_grid, from_=1, to=20, textvariable=self.num_ideas_var, width=8).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Target craft (optional)
        ttk.Label(controls_grid, text="Target craft (optional):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_craft_var = tk.StringVar()
        self.craft_combo = ttk.Combobox(controls_grid, textvariable=self.target_craft_var, state="readonly", width=25)
        self.craft_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Target creativity vector (optional)
        ttk.Label(controls_grid, text="Creativity approach (optional):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.target_vector_var = tk.StringVar()
        self.vector_combo = ttk.Combobox(controls_grid, textvariable=self.target_vector_var, state="readonly", width=25)
        self.vector_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # LLM Selection for Idea Generation
        ttk.Label(controls_grid, text="Idea Gen LLM Provider:").grid(row=0, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.idea_gen_provider_combo = ttk.Combobox(controls_grid, textvariable=self.idea_gen_llm_provider_var,
                                                   state="readonly", width=15)
        self.idea_gen_provider_combo.grid(row=0, column=3, padx=5, pady=5)
        self.idea_gen_provider_combo.bind('<<ComboboxSelected>>', self.on_idea_gen_provider_changed)

        ttk.Label(controls_grid, text="Idea Gen LLM Model:").grid(row=1, column=2, sticky=tk.W, padx=(20, 5), pady=5)
        self.idea_gen_model_combo = ttk.Combobox(controls_grid, textvariable=self.idea_gen_llm_model_var,
                                                state="readonly", width=25)
        self.idea_gen_model_combo.grid(row=1, column=3, padx=5, pady=5)

        # Generate button and progress bar frame
        button_frame = ttk.Frame(controls_frame)
        button_frame.pack(pady=15)

        self.generate_ideas_button = ttk.Button(button_frame, text="💡 Generate New Ideas", command=self.generate_ideas)
        self.generate_ideas_button.pack(side=tk.LEFT, padx=(0, 10))

        # Progress bar for idea generation (initially hidden)
        self.idea_progress = ttk.Progressbar(button_frame, mode='indeterminate', length=200)

        # Results area
        results_frame = ttk.LabelFrame(idea_frame, text="Generated Ideas", padding="8")
        results_frame.pack(fill=tk.BOTH, expand=True)

        self.ideas_text = scrolledtext.ScrolledText(results_frame, wrap=tk.WORD, height=12)
        self.ideas_text.pack(fill=tk.BOTH, expand=True)

        # Load creativity vectors and crafts
        self.load_generation_options()
    
    def create_log_tab(self):
        """Create the Log tab."""
        log_frame = ttk.Frame(self.notebook, padding="8")
        self.notebook.add(log_frame, text="Log")
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=(0, 8))
        
        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        
        # Log text area (more compact)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Configure log text tags for different message types
        self.log_text.tag_configure("info", foreground="black")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("error", foreground="red")
    
    def log_message(self, message: str, level: str = "info"):
        """Add a message to the log with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        # Check if log_text exists (in case this is called before GUI is fully initialized)
        if hasattr(self, 'log_text'):
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, formatted_message, level)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        # Also print to console
        print(formatted_message.strip())

    def populate_llm_dropdowns(self):
        """Populate LLM provider and model dropdowns."""
        try:
            # Get available providers
            providers = list(AVAILABLE_MODELS.keys())

            # Populate provider dropdowns
            self.writing_provider_combo['values'] = providers
            self.idea_gen_provider_combo['values'] = providers

            # Set default providers from config
            default_writing_provider = config_manager.get_pipeline_setting('writing_llm') or 'local'
            default_idea_provider = config_manager.get_pipeline_setting('analysis_llm') or 'local'  # Using analysis_llm as default for idea gen

            if default_writing_provider in providers:
                self.writing_llm_provider_var.set(default_writing_provider)
                self.on_writing_provider_changed()

            if default_idea_provider in providers:
                self.idea_gen_llm_provider_var.set(default_idea_provider)
                self.on_idea_gen_provider_changed()

        except Exception as e:
            self.log_message(f"Error populating LLM dropdowns: {e}", "error")

    def on_writing_provider_changed(self, event=None):
        """Handle writing LLM provider selection change."""
        try:
            provider = self.writing_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.writing_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('writing_llm_model')
                if default_model and default_model in models:
                    self.writing_llm_model_var.set(default_model)
                elif models:
                    self.writing_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating writing models: {e}", "error")

    def on_idea_gen_provider_changed(self, event=None):
        """Handle idea generation LLM provider selection change."""
        try:
            provider = self.idea_gen_llm_provider_var.get()
            if provider and provider in AVAILABLE_MODELS:
                models = AVAILABLE_MODELS[provider]
                self.idea_gen_model_combo['values'] = models

                # Set default model
                default_model = config_manager.get_pipeline_setting('analysis_llm_model')
                if default_model and default_model in models:
                    self.idea_gen_llm_model_var.set(default_model)
                elif models:
                    self.idea_gen_llm_model_var.set(models[0])

        except Exception as e:
            self.log_message(f"Error updating idea generation models: {e}", "error")

    def stop_operations(self):
        """Stop any running operations."""
        if self.is_running:
            self.stop_requested = True
            self.log_message("🛑 Stop requested - operations will terminate soon...", "warning")
            self.stop_button.config(state=tk.DISABLED)
        else:
            messagebox.showinfo("No Operations", "No operations are currently running.")

    def update_dashboard_status(self):
        """Update the dashboard status displays."""
        try:
            # Get database statistics
            stats = self.db.get_content_stats()

            # Update stats display
            stats_text = "Content Database Status:\n\n"
            total_content = sum(stats.values())

            for status, count in stats.items():
                percentage = (count / total_content * 100) if total_content > 0 else 0
                stats_text += f"• {status}: {count} ({percentage:.1f}%)\n"

            stats_text += f"\nTotal Content Ideas: {total_content}"

            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete('1.0', tk.END)
            self.stats_text.insert('1.0', stats_text)
            self.stats_text.config(state=tk.DISABLED)

            # Update next job info
            self.update_next_job_info()

        except Exception as e:
            self.log_message(f"Error updating dashboard status: {e}", "error")

    def update_next_job_info(self):
        """Update the next job information display."""
        try:
            # Get business logic settings
            freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            try:
                pillar_weights = json.loads(pillar_weights_str)
            except json.JSONDecodeError:
                pillar_weights = {}  # Fallback to empty dict

            # Get top candidate without selecting it
            scored_content = self.planner.calculate_freshness_scores(pillar_weights)

            if scored_content and scored_content[0]['freshness_score'] >= freshness_threshold:
                top_candidate = scored_content[0]
                # Store the next job candidate ID for highlighting
                self.next_job_candidate_id = top_candidate['id']
                job_text = f"""Ready to Execute:
Keyword: {top_candidate['keyword']}
Pillar: {top_candidate['pillar']} | Craft: {top_candidate['craft']}
Freshness Score: {top_candidate['freshness_score']:.1f} (Threshold: {freshness_threshold:.1f})
Angle: {top_candidate.get('proposed_angle', 'N/A')[:100]}..."""
            else:
                self.next_job_candidate_id = None
                if scored_content:
                    best_score = scored_content[0]['freshness_score']
                    job_text = f"""No job meets threshold:
Best candidate score: {best_score:.1f} (Threshold: {freshness_threshold:.1f})
Suggestion: Generate more ideas or lower threshold in Settings"""
                else:
                    job_text = """No content available:
Generate new ideas using the Idea Generator tab"""

            self.next_job_info.config(state=tk.NORMAL)
            self.next_job_info.delete('1.0', tk.END)
            self.next_job_info.insert('1.0', job_text)
            self.next_job_info.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error updating next job info: {e}", "error")

    def plan_next_job(self):
        """Plan the next content job (select and mark as PLANNED)."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def plan_thread():
            try:
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.DISABLED, text="Planning..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.job_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.job_progress.start(10))

                self.log_message("🔎 Planning next job...", "info")

                # Get business logic settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                try:
                    pillar_weights = json.loads(pillar_weights_str)
                except json.JSONDecodeError:
                    pillar_weights = {}  # Fallback to empty dict

                # Select next job
                job = self.planner.select_next_job(pillar_weights, freshness_threshold)

                if not job:
                    self.log_message("❌ No job selected - threshold not met or no content available", "warning")
                    return

                # Store the planned job
                self.current_planned_job = job
                self.log_message(f"✅ Planned job: '{job['keyword']}' (Score: {job['freshness_score']:.1f})", "success")

                # Update UI to show execution controls
                self.master.after(0, self.show_execution_controls)

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error planning job: {e}", "error")
            finally:
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.plan_job_button.config(state=tk.NORMAL, text="🔎 Plan Next Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.job_progress.stop())
                self.master.after(0, lambda: self.job_progress.pack_forget())

        self.current_thread = threading.Thread(target=plan_thread, daemon=True)
        self.current_thread.start()

    def show_execution_controls(self):
        """Show the execution controls section with planned job details."""
        if not self.current_planned_job:
            return

        # Update planned job details (compact format)
        job = self.current_planned_job
        job_text = f"Planned: {job['keyword']} | {job['pillar']} | Score: {job['freshness_score']:.1f}"

        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', job_text)
        self.planned_job_text.config(state=tk.DISABLED)

        # Enable execution controls
        self.execute_job_button.config(state=tk.NORMAL)

    def reset_execution_controls(self):
        """Reset the execution controls to their initial state."""
        # Clear planned job details
        self.planned_job_text.config(state=tk.NORMAL)
        self.planned_job_text.delete('1.0', tk.END)
        self.planned_job_text.insert('1.0', "No job planned. Use 'Plan Next Job' to select a job.")
        self.planned_job_text.config(state=tk.DISABLED)

        # Disable execute button
        self.execute_job_button.config(state=tk.DISABLED)

    def execute_planned_job(self):
        """Execute the planned job with selected options."""
        if not self.current_planned_job:
            messagebox.showwarning("No Planned Job", "Please plan a job first using the 'Plan Next Job' button.")
            return

        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def execute_thread():
            try:
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.execute_job_button.config(state=tk.DISABLED, text="Executing..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.execution_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.execution_progress.start(10))

                job = self.current_planned_job.copy()

                # Get execution options
                run_data_collection = self.run_data_collection_var.get()
                run_blog_writing = self.run_blog_writing_var.get()
                run_publish = self.run_publish_var.get()
                # Get multiple data source settings
                use_serpapi = self.use_serpapi_var.get()
                use_alsoasked = self.use_alsoasked_var.get()
                use_llm_suggestion = self.use_llm_suggestion_var.get()
                # Get publishing settings
                platform = self.publish_platform_var.get()
                publish_as_draft = self.publish_as_draft_var.get()

                self.log_message(f"🚀 Executing planned job: '{job['keyword']}'", "info")
                self.log_message(f"Steps: Data Collection={run_data_collection}, Writing={run_blog_writing}, Publishing={run_publish}", "info")
                self.log_message(f"Data Sources: SerpApi={use_serpapi}, AlsoAsked={use_alsoasked}, LLM={use_llm_suggestion}", "info")
                self.log_message(f"Platform: {platform}, Draft: {publish_as_draft}", "info")

                # Get selected LLM settings
                writing_provider = self.writing_llm_provider_var.get()
                writing_model = self.writing_llm_model_var.get()

                if writing_provider and writing_model:
                    self.log_message(f"Using {writing_provider} ({writing_model}) for content generation", "info")
                    job['override_writing_llm'] = writing_provider
                    job['override_writing_model'] = writing_model

                # Execute the job with options
                result = self.worker.execute_job(
                    job,
                    run_data_collection=run_data_collection,
                    run_blog_writing=run_blog_writing,
                    run_publish=run_publish,
                    use_serpapi=use_serpapi,
                    use_alsoasked=use_alsoasked,
                    use_llm_suggestion=use_llm_suggestion,
                    publish_platform=platform,
                    publish_as_draft=publish_as_draft
                )

                # Store the result for potential viewing
                self.last_job_execution_result = result

                if result['success']:
                    if result.get('platform_url'):
                        self.log_message(f"🎉 Job completed successfully! Published at: {result['platform_url']}", "success")
                    else:
                        self.log_message(f"🎉 Job completed successfully! (Publishing was skipped)", "success")
                        
                        # If content was written but not published, show helpful message
                        if result.get('blog_content') and not result.get('platform_url'):
                            self.log_message("💡 Content was generated and saved in database. You can access it later.", "info")
                else:
                    self.log_message(f"❌ Job failed: {'; '.join(result['errors'])}", "error")

                # Clear planned job and reset execution controls
                self.current_planned_job = None
                self.master.after(0, self.reset_execution_controls)

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error executing job: {e}", "error")
            finally:
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.execute_job_button.config(state=tk.NORMAL, text="🚀 Execute Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.execution_progress.stop())
                self.master.after(0, lambda: self.execution_progress.pack_forget())

        self.current_thread = threading.Thread(target=execute_thread, daemon=True)
        self.current_thread.start()

    def populate_manual_entry_dropdowns(self):
        """Populate the craft and pillar dropdowns for manual entry."""
        try:
            # Get business pillars from config
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ""

            crafts = []
            self.craft_pillar_mapping = {}

            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft, pillars_str = line.split('=', 1)
                    craft = craft.strip()
                    pillars = [p.strip() for p in pillars_str.split(',') if p.strip()]

                    if craft and pillars:
                        crafts.append(craft)
                        self.craft_pillar_mapping[craft] = pillars

            # Update craft dropdown
            self.manual_craft_combo['values'] = crafts

            # Clear pillar dropdown
            self.manual_pillar_combo['values'] = []

        except Exception as e:
            self.log_message(f"Error populating manual entry dropdowns: {e}", "error")

    def on_manual_craft_changed(self, event=None):
        """Handle craft selection change in manual entry."""
        try:
            selected_craft = self.manual_craft_var.get()
            if selected_craft in self.craft_pillar_mapping:
                pillars = self.craft_pillar_mapping[selected_craft]
                self.manual_pillar_combo['values'] = pillars
                # Clear current pillar selection
                self.manual_pillar_var.set("")
            else:
                self.manual_pillar_combo['values'] = []
                self.manual_pillar_var.set("")
        except Exception as e:
            self.log_message(f"Error updating pillar dropdown: {e}", "error")

    def add_manual_content_idea(self):
        """Add a manually entered content idea to the database."""
        try:
            # Get input values
            keyword = self.manual_keyword_var.get().strip()
            craft = self.manual_craft_var.get().strip()
            pillar = self.manual_pillar_var.get().strip()
            angle = self.manual_angle_var.get().strip()

            # Validate inputs
            if not keyword:
                messagebox.showerror("Validation Error", "Please enter a keyword/title.")
                return

            if not craft:
                messagebox.showerror("Validation Error", "Please select a craft.")
                return

            if not pillar:
                messagebox.showerror("Validation Error", "Please select a pillar.")
                return

            # Generate keyword vector embedding
            keyword_vector = None
            try:
                # Use the idea generator's embedding model
                if hasattr(self.idea_generator, 'embedding_model') and self.idea_generator.embedding_model:
                    vector = self.idea_generator.embedding_model.encode(keyword)
                    keyword_vector = self.db.serialize_vector(vector)
                else:
                    self.log_message("Warning: No embedding model available, adding without vector", "warning")
            except Exception as e:
                self.log_message(f"Warning: Could not generate embedding for keyword: {e}", "warning")

            # Insert into database
            content_id = self.db.insert_content_idea(
                keyword=keyword,
                pillar=pillar,
                craft=craft,
                proposed_angle=angle if angle else None,
                keyword_vector=keyword_vector
            )

            # Log success
            self.log_message(f"✅ User idea '{keyword}' added to Content Plan (ID: {content_id})", "success")

            # Clear input fields
            self.manual_keyword_var.set("")
            self.manual_craft_var.set("")
            self.manual_pillar_var.set("")
            self.manual_angle_var.set("")

            # Clear pillar dropdown
            self.manual_pillar_combo['values'] = []

            # Auto-calculate scores for new content, then refresh displays
            self.auto_calculate_scores()
            self.refresh_content_plan()
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"❌ Error adding manual content idea: {e}", "error")
            messagebox.showerror("Error", f"Failed to add content idea: {e}")

    def on_search_changed(self, *args):
        """Handle search text changes."""
        try:
            search_term = self.search_var.get().strip()
            if search_term:
                # Search in database
                matching_content = self.db.search_content(search_term)
                self.populate_content_tree(matching_content)
                self.content_status_var.set(f"Showing {len(matching_content)} matching content ideas")
            else:
                # Show all content
                self.refresh_content_plan()
        except Exception as e:
            self.log_message(f"Error during search: {e}", "error")

    def clear_search(self):
        """Clear the search field and show all content."""
        self.search_var.set("")
        self.refresh_content_plan()

    def sort_content_tree(self, column):
        """Sort the content tree by the specified column."""
        try:
            # Toggle sort direction if same column
            if self.sort_column == column:
                self.sort_reverse = not self.sort_reverse
            else:
                self.sort_column = column
                self.sort_reverse = False

            # Update column headers to show sort direction
            for col in ['#0', 'Status', 'Freshness', 'Keyword', 'Pillar', 'Proposed_Angle']:
                if col == column:
                    direction = ' ↓' if self.sort_reverse else ' ↑'
                    header_text = col.replace('#0', 'ID') + direction
                else:
                    header_text = col.replace('#0', 'ID') + ' ↕'
                self.content_tree.heading(col, text=header_text)

            # Sort the data
            if self.current_content_data:
                if column == '#0':
                    sort_key = lambda x: int(x.get('id', 0))
                elif column == 'Freshness':
                    sort_key = lambda x: float(x.get('freshness_score', 0))
                else:
                    sort_key = lambda x: str(x.get(column.lower(), ''))

                sorted_data = sorted(self.current_content_data, key=sort_key, reverse=self.sort_reverse)
                self.populate_content_tree(sorted_data)

        except Exception as e:
            self.log_message(f"Error sorting content tree: {e}", "error")

    def on_content_selection_changed(self, event):
        """Handle content selection change to update detail view."""
        try:
            selection = self.content_tree.selection()
            if not selection:
                # Clear detail view
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete('1.0', tk.END)
                self.detail_text.insert('1.0', "Select a content idea to view details...")
                self.detail_text.config(state=tk.DISABLED)
                return

            # Get selected item
            item = selection[0]
            content_id = self.content_tree.item(item, 'text')

            # Find the content data
            selected_content = None
            for content in self.current_content_data:
                if str(content['id']) == str(content_id):
                    selected_content = content
                    break

            if selected_content:
                # Safely handle freshness score formatting with utility function
                freshness_score = selected_content.get('freshness_score', 0)
                keyword = selected_content.get('keyword', 'Unknown')
                freshness_score = decode_freshness_score(freshness_score, keyword)
                
                # Format detailed information
                detail_text = f"""Content Idea Details

ID: {selected_content.get('id', 'N/A')}
Keyword: {selected_content.get('keyword', 'N/A')}
Status: {selected_content.get('status', 'N/A')}
Pillar: {selected_content.get('pillar', 'N/A')}
Craft: {selected_content.get('craft', 'N/A')}
Freshness Score: {freshness_score:.2f}

Proposed Angle:
{selected_content.get('proposed_angle', 'No angle specified')}

Created: {selected_content.get('created_date', 'N/A')}
Updated: {selected_content.get('updated_date', 'N/A')}

Platform URL: {selected_content.get('platform_url', 'Not published')}
Published Date: {selected_content.get('published_date', 'Not published')}

Vector Embedding: {'Available' if selected_content.get('keyword_vector') else 'Not available'}"""

                # Update detail view
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete('1.0', tk.END)
                self.detail_text.insert('1.0', detail_text)
                self.detail_text.config(state=tk.DISABLED)

        except Exception as e:
            self.log_message(f"Error updating detail view: {e}", "error")

    def populate_content_tree(self, content_data):
        """Populate the content tree with the given data."""
        try:
            # Store current data for sorting
            self.current_content_data = content_data

            # Clear existing items
            for item in self.content_tree.get_children():
                self.content_tree.delete(item)

            # Populate TreeView
            for content in content_data:
                # Safely extract and convert data to strings
                content_id = str(content.get('id', ''))
                status = str(content.get('status', ''))
                keyword = str(content.get('keyword', ''))
                pillar = str(content.get('pillar', ''))

                # Handle freshness score using the new utility function
                raw_freshness_score = content.get('freshness_score', 0)
                
                # Debug: Log what we're getting from the database
                if self.debug_mode:
                    print(f"🔍 GUI DEBUG [{keyword}]: Raw freshness_score from DB: {repr(raw_freshness_score)} (type: {type(raw_freshness_score)})")
                
                # Use the utility function for consistent decoding
                freshness_score = decode_freshness_score(raw_freshness_score, keyword)
                freshness_str = f"{freshness_score:.1f}"

                # Handle proposed angle safely and truncate for display
                proposed_angle = content.get('proposed_angle', '')
                if proposed_angle is None:
                    proposed_angle = ''
                proposed_angle = str(proposed_angle)
                if len(proposed_angle) > 50:
                    proposed_angle = proposed_angle[:47] + "..."

                # Determine the appropriate tag for highlighting
                tags = []
                if self.next_job_candidate_id and str(content.get('id')) == str(self.next_job_candidate_id):
                    tags.append('next_candidate')
                elif status == 'PLANNED':
                    tags.append('planned')
                elif status.startswith('REJECTED'):
                    tags.append('rejected')
                elif freshness_score >= 500:  # High priority threshold
                    tags.append('high_priority')

                self.content_tree.insert('', tk.END,
                                       text=content_id,
                                       values=(
                                           status,
                                           freshness_str,
                                           keyword,
                                           pillar,
                                           proposed_angle
                                       ),
                                       tags=tags)

        except Exception as e:
            self.log_message(f"Error populating content tree: {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

    def refresh_content_plan(self):
        """Refresh the content plan TreeView."""
        try:
            # Get all content
            all_content = self.db.get_all_content()
            
            # Debug: Log what we're getting from the database
            if self.debug_mode:
                self.log_message(f"🔄 GUI REFRESH: Retrieved {len(all_content)} items from database", "info")
                for i, content in enumerate(all_content[:3]):  # Log first 3 items
                    keyword = content.get('keyword', 'Unknown')
                    score = content.get('freshness_score', 'N/A')
                    self.log_message(f"  Item {i+1}: '{keyword}' = {score} (type: {type(score)})", "info")

            # Use the new populate method
            self.populate_content_tree(all_content)

            self.content_status_var.set(f"Showing {len(all_content)} content ideas")

        except Exception as e:
            self.log_message(f"Error refreshing content plan: {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")

    def prioritize_selected(self):
        """Set the selected content idea to highest priority."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to prioritize.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])

            # 1. Database Operation: Set freshness score to 999 to ensure it runs next
            self.db.update_freshness_score(content_id, 999.0)

            # 2. Log the Action
            self.log_message(f"⭐ Prioritized content ID {content_id}", "success")
            
            # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
            self.update_next_job_info()
            
            # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
            self.refresh_content_plan()
            
            # 5. Update Dashboard Statistics
            self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error prioritizing content: {e}", "error")

    def veto_selected(self):
        """Mark the selected content idea as rejected."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to veto.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])

            # Confirm the action
            if messagebox.askyesno("Confirm Veto", "Are you sure you want to reject this content idea?"):
                # 1. Database Operation: Update status to rejected
                self.db.update_content_status(content_id, 'REJECTED_USER')
                
                # 2. Log the Action
                self.log_message(f"❌ Vetoed content ID {content_id}", "info")
                
                # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                self.update_next_job_info()
                
                # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                self.refresh_content_plan()
                
                # 5. Update Dashboard Statistics
                self.update_dashboard_status()

        except Exception as e:
            self.log_message(f"Error vetoing content: {e}", "error")

    def delete_selected(self):
        """Delete the selected content idea from the database."""
        selected = self.content_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a content idea to delete.")
            return

        try:
            item = self.content_tree.item(selected[0])
            content_id = int(item['text'])
            
            # Get the content details for confirmation
            content = self.db.get_content_by_id(content_id)
            if not content:
                messagebox.showerror("Error", "Content not found in database.")
                return
            
            keyword = content.get('keyword', 'Unknown')
            
            # Confirm the deletion
            if messagebox.askyesno("Confirm Deletion", 
                                 f"Are you sure you want to permanently delete:\n\n'{keyword}'\n\nThis action cannot be undone."):
                # 1. Database Operation: Delete the content
                success = self.db.delete_content(content_id)
                if success:
                    # 2. Log the Action
                    self.log_message(f"🗑️ Deleted content: '{keyword}' (ID: {content_id})", "info")
                    
                    # 3. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                    self.update_next_job_info()
                    
                    # 4. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                    self.refresh_content_plan()
                    
                    # 5. Update Dashboard Statistics
                    self.update_dashboard_status()
                else:
                    messagebox.showerror("Error", "Failed to delete content from database.")

        except Exception as e:
            self.log_message(f"Error deleting content: {e}", "error")
            messagebox.showerror("Error", f"Failed to delete content: {e}")

    def open_cleanup_dialog(self):
        """Open a dialog for database cleanup operations."""
        cleanup_window = tk.Toplevel(self.master)
        cleanup_window.title("Database Cleanup")
        cleanup_window.geometry("500x400")
        cleanup_window.resizable(False, False)
        
        # Make it modal
        cleanup_window.transient(self.master)
        cleanup_window.grab_set()
        
        # Center the window
        cleanup_window.update_idletasks()
        x = (cleanup_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (cleanup_window.winfo_screenheight() // 2) - (400 // 2)
        cleanup_window.geometry(f"500x400+{x}+{y}")
        
        main_frame = ttk.Frame(cleanup_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Database Cleanup Operations", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))
        
        # Get current stats
        stats = self.db.get_content_stats()
        total = sum(stats.values())
        
        # Current stats display
        stats_frame = ttk.LabelFrame(main_frame, text="Current Database Statistics", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        stats_text = "\n".join([f"{status}: {count}" for status, count in stats.items()])
        stats_text += f"\n\nTotal: {total} content ideas"
        ttk.Label(stats_frame, text=stats_text).pack()
        
        # Cleanup operations
        ops_frame = ttk.LabelFrame(main_frame, text="Cleanup Operations", padding="10")
        ops_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete rejected content
        if stats.get('REJECTED_USER', 0) > 0:
            rejected_frame = ttk.Frame(ops_frame)
            rejected_frame.pack(fill=tk.X, pady=5)
            ttk.Label(rejected_frame, 
                     text=f"Delete {stats['REJECTED_USER']} rejected ideas:").pack(side=tk.LEFT)
            ttk.Button(rejected_frame, text="🗑️ Delete Rejected", 
                      command=lambda: self.cleanup_by_status('REJECTED_USER', cleanup_window)).pack(side=tk.RIGHT)
        
        # Delete old published content
        if stats.get('PUBLISHED', 0) > 0:
            published_frame = ttk.Frame(ops_frame)
            published_frame.pack(fill=tk.X, pady=5)
            ttk.Label(published_frame, 
                     text=f"Delete old published content (30+ days):").pack(side=tk.LEFT)
            ttk.Button(published_frame, text="🗑️ Delete Old Published", 
                      command=lambda: self.cleanup_old_published(cleanup_window)).pack(side=tk.RIGHT)
        
        # Reset all scores
        scores_frame = ttk.Frame(ops_frame)
        scores_frame.pack(fill=tk.X, pady=5)
        ttk.Label(scores_frame, text="Reset all freshness scores to 0:").pack(side=tk.LEFT)
        ttk.Button(scores_frame, text="🔄 Reset Scores", 
                  command=lambda: self.reset_all_scores(cleanup_window)).pack(side=tk.RIGHT)
        
        # Dangerous operations
        danger_frame = ttk.LabelFrame(main_frame, text="⚠️ Dangerous Operations", padding="10")
        danger_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Delete all content
        danger_ops = ttk.Frame(danger_frame)
        danger_ops.pack(fill=tk.X, pady=5)
        ttk.Label(danger_ops, text="DELETE ALL CONTENT (cannot be undone):", 
                 foreground="red").pack(side=tk.LEFT)
        ttk.Button(danger_ops, text="💥 DELETE ALL", 
                  command=lambda: self.delete_all_content(cleanup_window)).pack(side=tk.RIGHT)
        
        # Close button
        ttk.Button(main_frame, text="Close", 
                  command=cleanup_window.destroy).pack(pady=10)
    
    def cleanup_by_status(self, status: str, parent_window):
        """Delete all content with a specific status."""
        try:
            content_list = self.db.get_content_by_status(status)
            count = len(content_list)
            
            if count == 0:
                messagebox.showinfo("No Content", f"No content found with status '{status}'.")
                return
            
            if messagebox.askyesno("Confirm Cleanup", 
                                 f"Delete {count} content ideas with status '{status}'?\n\nThis cannot be undone.",
                                 parent=parent_window):
                deleted = 0
                for content in content_list:
                    if self.db.delete_content(content['id']):
                        deleted += 1
                
                self.log_message(f"🧹 Cleaned up {deleted} content ideas with status '{status}'", "success")
                messagebox.showinfo("Cleanup Complete", f"Deleted {deleted} content ideas.", parent=parent_window)
                
                # Refresh displays
                self.refresh_content_plan()
                self.update_dashboard_status()
                parent_window.destroy()
                
        except Exception as e:
            self.log_message(f"Error during cleanup: {e}", "error")
            messagebox.showerror("Error", f"Cleanup failed: {e}", parent=parent_window)
    
    def cleanup_old_published(self, parent_window):
        """Delete published content older than 30 days."""
        try:
            # This would need to be implemented in the database module
            # For now, just show a message
            messagebox.showinfo("Feature Coming Soon", 
                              "Old published content cleanup will be implemented in a future version.",
                              parent=parent_window)
        except Exception as e:
            self.log_message(f"Error during old published cleanup: {e}", "error")
            messagebox.showerror("Error", f"Cleanup failed: {e}", parent=parent_window)
    
    def reset_all_scores(self, parent_window):
        """Reset all freshness scores to 0."""
        try:
            if messagebox.askyesno("Confirm Reset", 
                                 "Reset ALL freshness scores to 0?\n\nYou can recalculate them later.",
                                 parent=parent_window):
                # Get all content and reset scores
                all_content = self.db.get_all_content()
                updated = 0
                
                for content in all_content:
                    if self.db.update_freshness_score(content['id'], 0.0):
                        updated += 1
                
                self.log_message(f"🔄 Reset {updated} freshness scores", "success")
                messagebox.showinfo("Reset Complete", f"Reset {updated} freshness scores.", parent=parent_window)
                
                # Refresh displays
                self.refresh_content_plan()
                self.update_dashboard_status()
                parent_window.destroy()
                
        except Exception as e:
            self.log_message(f"Error resetting scores: {e}", "error")
            messagebox.showerror("Error", f"Reset failed: {e}", parent=parent_window)
    
    def delete_all_content(self, parent_window):
        """Delete ALL content from the database."""
        try:
            # Double confirmation for dangerous operation
            if messagebox.askyesno("⚠️ DANGER ⚠️", 
                                 "This will DELETE ALL CONTENT from your database!\n\nAre you ABSOLUTELY sure?",
                                 parent=parent_window):
                if messagebox.askyesno("⚠️ FINAL WARNING ⚠️", 
                                     "Last chance! This CANNOT be undone!\n\nDelete everything?",
                                     parent=parent_window):
                    all_content = self.db.get_all_content()
                    deleted = 0
                    
                    for content in all_content:
                        if self.db.delete_content(content['id']):
                            deleted += 1
                    
                    self.log_message(f"💥 DELETED ALL CONTENT - {deleted} ideas removed", "warning")
                    messagebox.showinfo("Database Cleared", f"Deleted {deleted} content ideas.\n\nDatabase is now empty.", 
                                       parent=parent_window)
                    
                    # Refresh displays
                    self.refresh_content_plan()
                    self.update_dashboard_status()
                    parent_window.destroy()
                    
        except Exception as e:
            self.log_message(f"Error deleting all content: {e}", "error")
            messagebox.showerror("Error", f"Delete all failed: {e}", parent=parent_window)

    def refresh_all_scores(self):
        """Refresh all freshness scores using current settings."""
        def refresh_thread():
            try:
                self.log_message("🔄 Refreshing all freshness scores...", "info")

                # Get current settings
                freshness_threshold = float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0)
                pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
                try:
                    pillar_weights = json.loads(pillar_weights_str)
                except json.JSONDecodeError:
                    pillar_weights = {}  # Fallback to empty dict

                # 1. Calculate scores (updates DB)
                scored_content = self.planner.calculate_freshness_scores(pillar_weights)

                self.log_message(f"✅ Refreshed scores for {len(scored_content)} content ideas", "success")
                
                # Debug: Log a few scores to verify they're calculated
                if scored_content:
                    self.log_message("Top 3 calculated scores:", "info")
                    for i, content in enumerate(scored_content[:3]):
                        keyword = content.get('keyword', 'Unknown')
                        score = content.get('freshness_score', 0)
                        self.log_message(f"  {i+1}. '{keyword}': {score:.1f}", "info")

                # Force refresh displays with correct sequence after database updates are committed
                import time
                time.sleep(0.1)  # Small delay to ensure DB updates are committed
                
                # 2. Update "Next Job Candidate" Logic FIRST (recalculates scores and identifies new top candidate)
                self.master.after(100, self.update_next_job_info)
                
                # 3. Refresh Content Plan TreeView (uses updated next_job_candidate_id for highlighting)
                self.master.after(200, self.refresh_content_plan)
                
                # 4. Update Dashboard Statistics
                self.master.after(300, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error refreshing scores: {e}", "error")
                import traceback
                print(f"Full traceback: {traceback.format_exc()}")

        threading.Thread(target=refresh_thread, daemon=True).start()

    def auto_calculate_scores(self):
        """Automatically calculate freshness scores with default settings."""
        try:
            # Get default settings for scoring
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            try:
                pillar_weights = json.loads(pillar_weights_str)
            except json.JSONDecodeError:
                # If no valid pillar weights, create some basic ones
                business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
                pillar_weights = {}
                for line in business_pillars.split('\n'):
                    line = line.strip()
                    if '=' in line:
                        craft = line.split('=', 1)[0].strip()
                        if craft:
                            pillar_weights[craft] = 1.0  # Default weight
            
            # Calculate scores silently in background
            def score_thread():
                try:
                    scored_content = self.planner.calculate_freshness_scores(pillar_weights)
                    self.log_message(f"📊 Auto-calculated scores for {len(scored_content)} content ideas", "info")
                    
                    # Refresh the content plan to show updated scores
                    self.master.after(0, self.refresh_content_plan)
                except Exception as e:
                    self.log_message(f"Error auto-calculating scores: {e}", "warning")
            
            threading.Thread(target=score_thread, daemon=True).start()
            
        except Exception as e:
            self.log_message(f"Error starting auto-calculation: {e}", "error")

    def open_database_file(self):
        """Open the database file location in file explorer."""
        try:
            import os
            import subprocess
            import platform
            
            db_path = self.db.db_path
            db_dir = os.path.dirname(db_path)
            
            # Show info about the database
            stats = self.db.get_content_stats()
            total = sum(stats.values())
            
            info_text = f"""Database Information:

File Location: {db_path}
Total Content Ideas: {total}

You can:
• Use DB Browser for SQLite to view/edit the database
• Back up the .db file to save your content
• Delete the .db file to start fresh (will recreate automatically)

Open the database folder?"""
            
            if messagebox.askyesno("Database Access", info_text):
                # Open the folder containing the database
                if platform.system() == "Windows":
                    os.startfile(db_dir)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", db_dir])
                else:  # Linux
                    subprocess.run(["xdg-open", db_dir])
                    
                self.log_message(f"📁 Opened database folder: {db_dir}", "info")
                
        except Exception as e:
            self.log_message(f"Error opening database folder: {e}", "error")
            messagebox.showerror("Error", f"Could not open database folder: {e}")

    def export_to_csv(self):
        """Export all content to a CSV file for easy viewing/editing."""
        try:
            from tkinter import filedialog
            import csv
            import os
            from datetime import datetime
            
            # Get all content
            all_content = self.db.get_all_content()
            
            if not all_content:
                messagebox.showinfo("No Data", "No content found in database to export.")
                return
            
            # Default filename with timestamp
            default_filename = f"content_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            # Ask user where to save
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export Content to CSV",
                initialvalue=default_filename
            )
            
            if not filename:
                return  # User cancelled
            
            # Export to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                # Define fieldnames (excluding binary data)
                fieldnames = ['id', 'keyword', 'pillar', 'craft', 'status', 'proposed_angle', 
                             'freshness_score', 'platform_url', 'published_date', 'created_date', 'updated_date']
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for content in all_content:
                    # Create a clean row without binary data
                    row = {}
                    for field in fieldnames:
                        value = content.get(field, '')
                        # Handle special cases
                        if field == 'freshness_score':
                            if isinstance(value, bytes):
                                value = 0.0
                            try:
                                value = float(value) if value else 0.0
                            except (ValueError, TypeError):
                                value = 0.0
                        elif value is None:
                            value = ''
                        row[field] = value
                    
                    writer.writerow(row)
            
            self.log_message(f"📊 Exported {len(all_content)} content ideas to {filename}", "success")
            messagebox.showinfo("Export Complete", 
                              f"Exported {len(all_content)} content ideas to:\n\n{filename}\n\nYou can now view and edit this in Excel or any spreadsheet program.")
            
        except Exception as e:
            self.log_message(f"Error exporting to CSV: {e}", "error")
            messagebox.showerror("Export Error", f"Failed to export content: {e}")

    def generate_ideas(self):
        """Generate new content ideas in a background thread."""
        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def generate_thread():
            try:
                # Set running state and update UI
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.DISABLED, text="Generating Ideas..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.idea_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.idea_progress.start(10))

                self.log_message("💡 Generating new content ideas...", "info")

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get settings
                num_ideas = self.num_ideas_var.get()
                target_craft = self.target_craft_var.get() or None
                target_vector = self.target_vector_var.get() or None

                # Get business pillars from settings
                business_pillars = config_manager.get_pipeline_setting('business_pillars')
                if not business_pillars:
                    self.log_message("❌ No business pillars configured. Please set them in Settings > Business Logic", "error")
                    return

                # Check for stop request
                if self.stop_requested:
                    self.log_message("🛑 Idea generation stopped by user", "warning")
                    return

                # Get selected LLM settings for idea generation
                idea_provider = self.idea_gen_llm_provider_var.get()
                idea_model = self.idea_gen_llm_model_var.get()

                if idea_provider and idea_model:
                    self.log_message(f"Using {idea_provider} ({idea_model}) for idea generation", "info")
                    # Override the idea generator's LLM settings
                    original_provider = config_manager.get_pipeline_setting('analysis_llm')
                    original_model = config_manager.get_pipeline_setting('analysis_llm_model')

                    # Temporarily update config for this generation
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', idea_provider)
                    config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', idea_model)

                try:
                    # Check for stop request before generating
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Generate ideas
                    ideas = self.idea_generator.generate_ideas(
                        pillars_text=business_pillars,
                        target_craft=target_craft,
                        target_creativity_vector=target_vector,
                        num_ideas=num_ideas
                    )

                    # Check for stop request after generation
                    if self.stop_requested:
                        self.log_message("🛑 Idea generation stopped by user", "warning")
                        return

                    # Display results
                    results_text = f"Generated {len(ideas)} new content ideas:\n\n"
                    for i, idea in enumerate(ideas, 1):
                        results_text += f"{i}. {idea['keyword']}\n"
                        results_text += f"   Pillar: {idea['pillar']} | Craft: {idea['craft']}\n"
                        results_text += f"   Angle: {idea['proposed_angle']}\n\n"

                    self.master.after(0, lambda: self.display_generated_ideas(results_text))
                    self.log_message(f"✅ Successfully generated {len(ideas)} new ideas", "success")

                    # Refresh displays
                    self.master.after(0, self.refresh_content_plan)
                    self.master.after(0, self.update_dashboard_status)

                finally:
                    # Restore original LLM settings if they were overridden
                    if idea_provider and idea_model:
                        if original_provider:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm', original_provider)
                        if original_model:
                            config_manager.update_setting('PIPELINE_SETTINGS', 'analysis_llm_model', original_model)

            except Exception as e:
                self.log_message(f"❌ Error generating ideas: {e}", "error")
            finally:
                # Reset running state and UI
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.generate_ideas_button.config(state=tk.NORMAL, text="💡 Generate New Ideas"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.idea_progress.stop())
                self.master.after(0, lambda: self.idea_progress.pack_forget())

        threading.Thread(target=generate_thread, daemon=True).start()

    def display_generated_ideas(self, text: str):
        """Display generated ideas in the text area."""
        self.ideas_text.delete('1.0', tk.END)
        self.ideas_text.insert('1.0', text)

    def load_generation_options(self):
        """Load creativity vectors and crafts into the comboboxes."""
        try:
            # Load creativity vectors
            vectors = self.idea_generator.get_creativity_vectors()
            self.vector_combo['values'] = [''] + vectors

            # Load crafts from business pillars
            business_pillars = config_manager.get_pipeline_setting('business_pillars') or ''
            crafts = []
            for line in business_pillars.split('\n'):
                line = line.strip()
                if '=' in line:
                    craft = line.split('=', 1)[0].strip()
                    if craft:
                        crafts.append(craft)

            self.craft_combo['values'] = [''] + crafts

        except Exception as e:
            self.log_message(f"Error loading generation options: {e}", "error")



    def open_settings(self):
        """Open the settings window."""
        settings_window = tk.Toplevel(self.master)
        SettingsWindow(settings_window)

        # Refresh data after settings window closes
        settings_window.wait_window()
        self.load_generation_options()
        self.populate_llm_dropdowns()  # Refresh LLM dropdowns in case models were updated
        self.update_dashboard_status()

    def show_database_stats(self):
        """Show detailed database statistics."""
        try:
            stats = self.db.get_content_stats()
            total = sum(stats.values())

            stats_text = "Detailed Database Statistics:\n\n"
            for status, count in stats.items():
                percentage = (count / total * 100) if total > 0 else 0
                stats_text += f"{status}: {count} ({percentage:.1f}%)\n"

            stats_text += f"\nTotal Content Ideas: {total}"

            # Get queue summary
            queue_summary = self.planner.get_queue_summary()
            stats_text += f"\nContent in Queue: {queue_summary['total_in_queue']}"

            messagebox.showinfo("Database Statistics", stats_text)

        except Exception as e:
            self.log_message(f"Error getting database stats: {e}", "error")

    def show_about(self):
        """Show about dialog."""
        about_text = """Content Strategist Dashboard

A proactive, database-driven content planning and execution system.

Features:
• Strategic content planning with freshness scoring
• Automated content idea generation
• Complete content lifecycle management
• Direct publishing to Shopify and WordPress
• Real-time progress monitoring

Built with Python and powered by advanced LLM integration."""

        messagebox.showinfo("About Content Strategist", about_text)

    def clear_log(self):
        """Clear the log text area."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state=tk.DISABLED)

    def force_refresh_gui(self):
        """Force refresh the GUI to debug display issues."""
        try:
            self.log_message("🔄 FORCE REFRESH: Starting GUI debug refresh...", "info")
            
            # Directly query database and log raw data
            self.log_message("🔍 Step 1: Querying database directly...", "info")
            all_content = self.db.get_all_content()
            
            self.log_message(f"🔍 Step 2: Retrieved {len(all_content)} items from database", "info")
            
            # Log raw database values for first few items
            for i, content in enumerate(all_content[:3]):
                keyword = content.get('keyword', 'Unknown')
                raw_score = content.get('freshness_score')
                self.log_message(f"  Raw DB item {i+1}: '{keyword}' = {repr(raw_score)} (type: {type(raw_score)})", "info")
            
            self.log_message("🔍 Step 3: Refreshing TreeView...", "info")
            
            # Clear and repopulate with verbose debugging
            for item in self.content_tree.get_children():
                self.content_tree.delete(item)
                
            # Re-populate with debug output
            self.populate_content_tree(all_content)
            
            self.log_message("✅ FORCE REFRESH: Complete", "success")
            
        except Exception as e:
            self.log_message(f"❌ FORCE REFRESH: Error - {e}", "error")
            import traceback
            print(f"Full traceback: {traceback.format_exc()}")
    
    def show_scoring_debug(self):
        """Show detailed scoring debug information."""
        try:
            debug_info = self.planner.get_scoring_debug_info()
            
            if not debug_info:
                messagebox.showinfo("Scoring Debug", "No scoring debug information available.\n\nRun 'Refresh All Scores' or 'Auto-Calculate Scores' first to generate debug data.")
                return
            
            # Create debug window
            debug_window = tk.Toplevel(self.master)
            debug_window.title("🔍 Freshness Scoring Debug Information")
            debug_window.geometry("800x600")
            debug_window.resizable(True, True)
            debug_window.transient(self.master)
            
            # Create main frame with scrolling
            main_frame = ttk.Frame(debug_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create scrolled text widget
            debug_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, font=("Consolas", 10))
            debug_text.pack(fill=tk.BOTH, expand=True)
            
            # Build debug information
            debug_content = "🔍 FRESHNESS SCORING DEBUG REPORT\n"
            debug_content += "=" * 50 + "\n\n"
            
            # Embedding status
            debug_content += f"🧠 EMBEDDING STATUS: {debug_info.get('embedding_status', 'Unknown')}\n\n"
            
            # Content counts
            debug_content += f"📊 CONTENT STATISTICS:\n"
            debug_content += f"   • Content scored: {debug_info.get('content_count', 0)}\n"
            debug_content += f"   • Published content for comparison: {debug_info.get('published_count', 0)}\n\n"
            
            # Scoring settings
            settings = debug_info.get('settings', {})
            debug_content += f"⚙️ SCORING SETTINGS:\n"
            debug_content += f"   • Time weight: {settings.get('time_weight', 'N/A')}\n"
            debug_content += f"   • Uniqueness weight: {settings.get('uniqueness_weight', 'N/A')}\n"
            debug_content += f"   • Max angle bonus: {settings.get('max_angle_bonus', 'N/A')}\n\n"
            
            # Pillar weights
            pillar_weights = debug_info.get('pillar_weights', {})
            debug_content += f"⚖️ PILLAR WEIGHTS:\n"
            if pillar_weights:
                for craft, weight in pillar_weights.items():
                    debug_content += f"   • {craft}: {weight}\n"
            else:
                debug_content += "   • No pillar weights configured\n"
            debug_content += "\n"
            
            # Zero scores warning
            zero_scores = debug_info.get('zero_scores', [])
            if zero_scores:
                debug_content += f"⚠️ ITEMS WITH 0.0 SCORES ({len(zero_scores)}):  \n"
                for keyword in zero_scores:
                    debug_content += f"   • {keyword}\n"
                debug_content += "\n"
                debug_content += "💡 TROUBLESHOOTING TIPS FOR 0.0 SCORES:\n"
                debug_content += "   1. Check if sentence-transformers is installed: pip install sentence-transformers\n"
                debug_content += "   2. Ensure keyword vectors are being generated (check embedding status above)\n"
                debug_content += "   3. Verify pillar weights are configured correctly\n"
                debug_content += "   4. Check if proposed angles are present (angle bonus = 0 if empty)\n"
                debug_content += "   5. Look at the Log tab for detailed per-item debug output\n\n"
            
            # Instructions
            debug_content += "📝 HOW TO USE THIS INFORMATION:\n"
            debug_content += "   • If embedding status shows 'Failed to load', install sentence-transformers\n"
            debug_content += "   • If many items have 0.0 scores, check embedding status and pillar weights\n"
            debug_content += "   • Detailed per-item scoring appears in the Log tab when debug mode is on\n"
            debug_content += "   • Adjust pillar weights in Settings > Business Logic to influence scoring\n\n"
            
            debug_content += "🔄 To get fresh debug data, use 'Tools > Refresh All Scores'\n"
            
            # Insert content and make read-only
            debug_text.insert('1.0', debug_content)
            debug_text.config(state=tk.DISABLED)
            
            # Add close button
            close_button = ttk.Button(main_frame, text="Close", command=debug_window.destroy)
            close_button.pack(pady=10)
            
        except Exception as e:
            self.log_message(f"Error showing scoring debug: {e}", "error")
            messagebox.showerror("Debug Error", f"Could not display debug information: {e}")

    def show_embedding_status(self):
        """Show embedding model status."""
        try:
            # Get actual embedding status from both idea generator and planner
            idea_gen_status = getattr(self.idea_generator, 'embedding_status', 'Unknown')
            planner_status = self.planner.get_embedding_status()
            
            # Create status window
            status_window = tk.Toplevel(self.master)
            status_window.title("🧠 Embedding Model Status")
            status_window.geometry("600x500")
            status_window.resizable(True, True)
            status_window.transient(self.master)
            
            # Create main frame with scrolling
            main_frame = ttk.Frame(status_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Create scrolled text widget
            status_text = scrolledtext.ScrolledText(main_frame, wrap=tk.WORD, font=("Consolas", 10))
            status_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            
            # Build status information
            status_content = "🧠 EMBEDDING MODEL STATUS REPORT\n"
            status_content += "=" * 45 + "\n\n"
            
            # Model information
            status_content += "🎨 MODEL INFORMATION:\n"
            status_content += "   • Model: all-MiniLM-L6-v2\n"
            status_content += "   • Provider: SentenceTransformers\n"
            status_content += "   • Purpose: Semantic similarity and uniqueness scoring\n\n"
            
            # Status for each component
            status_content += "🔧 COMPONENT STATUS:\n"
            status_content += f"   • Idea Generator: {idea_gen_status}\n"
            status_content += f"   • Content Planner: {planner_status}\n\n"
            
            # Check overall status
            embedding_working = ("Success" in idea_gen_status and "Success" in planner_status)
            
            if embedding_working:
                status_content += "✅ OVERALL STATUS: OPERATIONAL\n\n"
                status_content += "🔍 WHAT THIS ENABLES:\n"
                status_content += "   • Advanced semantic similarity calculations\n"
                status_content += "   • Accurate content uniqueness scoring\n"
                status_content += "   • Intelligent duplicate detection\n"
                status_content += "   • Better content recommendation\n\n"
                
                status_content += "📊 PERFORMANCE IMPACT:\n"
                status_content += "   • More accurate freshness scoring\n"
                status_content += "   • Better job selection algorithm\n"
                status_content += "   • Improved content diversity\n\n"
            else:
                status_content += "⚠️ OVERALL STATUS: LIMITED FUNCTIONALITY\n\n"
                status_content += "🔧 CURRENT LIMITATIONS:\n"
                status_content += "   • Using simple keyword-based similarity\n"
                status_content += "   • Less accurate uniqueness scoring\n"
                status_content += "   • Reduced duplicate detection capability\n\n"
                
                status_content += "🛠️ HOW TO FIX:\n"
                status_content += "   1. Install sentence-transformers:\n"
                status_content += "      pip install sentence-transformers\n\n"
                status_content += "   2. Restart the application\n\n"
                status_content += "   3. Check the Log tab for any error messages\n\n"
                
                status_content += "💡 FALLBACK BEHAVIOR:\n"
                status_content += "   • System uses keyword overlap for similarity\n"
                status_content += "   • Scoring still works but less accurately\n"
                status_content += "   • No loss of core functionality\n\n"
            
            # Technical details
            status_content += "🔍 TECHNICAL DETAILS:\n"
            status_content += "   • Vector dimensions: 384\n"
            status_content += "   • Context window: 256 tokens\n"
            status_content += "   • Similarity metric: Cosine similarity\n"
            status_content += "   • Storage: Binary serialized in database\n\n"
            
            # Insert content and make read-only
            status_text.insert('1.0', status_content)
            status_text.config(state=tk.DISABLED)
            
            # Add buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=5)
            
            if not embedding_working:
                install_button = ttk.Button(button_frame, text="💲 Install sentence-transformers", 
                                           command=self.install_sentence_transformers)
                install_button.pack(side=tk.LEFT, padx=(0, 10))
            
            close_button = ttk.Button(button_frame, text="Close", command=status_window.destroy)
            close_button.pack(side=tk.LEFT)
            
        except Exception as e:
            self.log_message(f"Error showing embedding status: {e}", "error")
            messagebox.showerror("Status Error", f"Could not display embedding status: {e}")
    
    def install_sentence_transformers(self):
        """Helper function to install sentence-transformers."""
        import subprocess
        import sys
        
        try:
            self.log_message("💲 Installing sentence-transformers...", "info")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "sentence-transformers"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message("✅ sentence-transformers installed successfully!", "success")
                messagebox.showinfo("Installation Complete", 
                                   "sentence-transformers has been installed successfully!\n\n"
                                   "Please restart the Content Strategist application to enable "
                                   "advanced embedding features.")
            else:
                self.log_message(f"❌ Installation failed: {result.stderr}", "error")
                messagebox.showerror("Installation Failed", 
                                    f"Failed to install sentence-transformers:\n\n{result.stderr}")
                
        except Exception as e:
            self.log_message(f"❌ Error during installation: {e}", "error")
            messagebox.showerror("Installation Error", f"Error during installation: {e}")

    def on_closing(self):
        """Handle window closing."""
        if self.is_running:
            if messagebox.askokcancel("Job Running", "A job is currently running. Do you want to exit anyway?"):
                self.master.destroy()
        else:
            self.master.destroy()
