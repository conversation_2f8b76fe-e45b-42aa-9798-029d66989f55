    def execute_planned_job(self):
        """Execute the planned job with selected options."""
        if not self.current_planned_job:
            messagebox.showwarning("No Planned Job", "Please plan a job first using the 'Plan Next Job' button.")
            return

        if self.is_running:
            messagebox.showwarning("Operation Running", "An operation is already running. Please wait for it to complete.")
            return

        def execute_thread():
            try:
                self.is_running = True
                self.stop_requested = False
                self.master.after(0, lambda: self.execute_job_button.config(state=tk.DISABLED, text="Executing..."))
                self.master.after(0, lambda: self.stop_button.config(state=tk.NORMAL))
                self.master.after(0, lambda: self.execution_progress.pack(side=tk.LEFT, padx=(0, 10)))
                self.master.after(0, lambda: self.execution_progress.start(10))

                job = self.current_planned_job.copy()

                # Get execution options
                run_data_collection = self.run_data_collection_var.get()
                run_blog_writing = self.run_blog_writing_var.get()
                run_publish = self.run_publish_var.get()
                
                # Get multiple data source settings
                use_serpapi = self.use_serpapi_var.get()
                use_alsoasked = self.use_alsoasked_var.get()
                use_llm_suggestion = self.use_llm_suggestion_var.get()
                
                # Get publishing settings
                platform = self.publish_platform_var.get()
                publish_as_draft = self.publish_as_draft_var.get()

                self.log_message(f"🚀 Executing planned job: '{job['keyword']}'", "info")
                self.log_message(f"Steps: Data Collection={run_data_collection}, Writing={run_blog_writing}, Publishing={run_publish}", "info")
                self.log_message(f"Data Sources: SerpApi={use_serpapi}, AlsoAsked={use_alsoasked}, LLM={use_llm_suggestion}", "info")
                self.log_message(f"Platform: {platform}, Draft: {publish_as_draft}", "info")

                # Get selected LLM settings
                writing_provider = self.writing_llm_provider_var.get()
                writing_model = self.writing_llm_model_var.get()

                if writing_provider and writing_model:
                    self.log_message(f"Using {writing_provider} ({writing_model}) for content generation", "info")
                    job['override_writing_llm'] = writing_provider
                    job['override_writing_model'] = writing_model

                # Execute the job with the new parameters
                result = self.worker.execute_job(
                    job,
                    run_data_collection=run_data_collection,
                    run_blog_writing=run_blog_writing,
                    run_publish=run_publish,
                    use_serpapi=use_serpapi,
                    use_alsoasked=use_alsoasked,
                    use_llm_suggestion=use_llm_suggestion,
                    publish_platform=platform,
                    publish_as_draft=publish_as_draft
                )

                # Store the result for potential viewing
                self.last_job_execution_result = result

                if result['success']:
                    if result.get('platform_url'):
                        self.log_message(f"🎉 Job completed successfully! Published at: {result['platform_url']}", "success")
                    else:
                        self.log_message(f"🎉 Job completed successfully! (Publishing was skipped)", "success")
                        
                    # If content was written but not published, show helpful message
                    if result.get('blog_content') and not result.get('platform_url'):
                        self.log_message("💡 Content was generated and saved in database. You can access it later.", "info")
                else:
                    self.log_message(f"❌ Job failed: {'; '.join(result['errors'])}", "error")

                # Clear planned job and reset execution controls
                self.current_planned_job = None
                self.master.after(0, self.reset_execution_controls)

                # Refresh displays
                self.master.after(0, self.refresh_content_plan)
                self.master.after(0, self.update_dashboard_status)

            except Exception as e:
                self.log_message(f"❌ Error executing job: {e}", "error")
            finally:
                self.is_running = False
                self.stop_requested = False
                self.master.after(0, lambda: self.execute_job_button.config(state=tk.NORMAL, text="🚀 Execute Job"))
                self.master.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
                self.master.after(0, lambda: self.execution_progress.stop())
                self.master.after(0, lambda: self.execution_progress.pack_forget())

        self.current_thread = threading.Thread(target=execute_thread, daemon=True)
        self.current_thread.start()
