#!/usr/bin/env python3
"""
Test script to verify the freshness score byte decoding fix.

This script tests the decode_freshness_score function with various input types
including the actual bytes that were found in your database.
"""

import struct
from gui.main_window import decode_freshness_score

def test_byte_decoding():
    """Test the byte decoding functionality with real examples from your database."""
    
    print("🧪 Testing Freshness Score Byte Decoding Fix")
    print("=" * 50)
    
    # Test cases from your actual database
    test_cases = [
        # Real bytes from your database logs
        (b'\xe4\xf1\x81B', 'leather watch strap care'),
        (b'\xc9\xe3{B', 'leather watch strap maintenance tips'),
        (b'\x9f\x9b\x8cB', 'restoring old leather watch straps'),
        (b'\x81\x8a\xb5B', "men's skincare microbiome"),
        (b'\x81\x1f\x8bB', 'conditioning leather watch bands'),
        (b'm\xbe\x8aB', 'artisan shaving soap'),
        (b'Y\xb1}B', 'handmade shaving soap Australia'),
        (b'Y\xb1uB', 'Australian shaving soap'),
        (b'$\xcb\x88B', 'eucalyptus shaving soap'),
        (b'"\xaa\x91B', 'how to waterproof leather watch strap'),
        (b'\x11\xff\x90B', 'shaving soap for sensitive skin'),
        
        # Additional test cases
        (None, 'null test'),
        (0, 'zero test'),
        (42.5, 'normal float test'),
        ('123.45', 'string number test'),
        ('invalid', 'invalid string test'),
        (b'', 'empty bytes test'),
        (b'\x01\x02\x03', 'invalid bytes test'),
    ]
    
    print("Test Results:")
    print("-" * 50)
    
    for i, (input_value, description) in enumerate(test_cases, 1):
        try:
            decoded_value = decode_freshness_score(input_value, description)
            
            # If it's bytes, also show what the original value should have been
            if isinstance(input_value, bytes) and len(input_value) == 4:
                try:
                    original_float = struct.unpack('f', input_value)[0]
                    print(f"{i:2d}. {description}")
                    print(f"    Input: {repr(input_value)}")
                    print(f"    Expected: {original_float:.2f}")
                    print(f"    Decoded: {decoded_value:.2f}")
                    print(f"    Status: {'✅ PASS' if abs(decoded_value - original_float) < 0.01 else '❌ FAIL'}")
                except:
                    print(f"{i:2d}. {description}")
                    print(f"    Input: {repr(input_value)}")
                    print(f"    Decoded: {decoded_value:.2f}")
                    print(f"    Status: ✅ HANDLED")
            else:
                print(f"{i:2d}. {description}")
                print(f"    Input: {repr(input_value)}")
                print(f"    Decoded: {decoded_value:.2f}")
                print(f"    Status: ✅ HANDLED")
                
        except Exception as e:
            print(f"{i:2d}. {description}")
            print(f"    Input: {repr(input_value)}")
            print(f"    Error: {e}")
            print(f"    Status: ❌ ERROR")
        
        print()
    
    print("=" * 50)
    print("🎯 Testing Summary:")
    print("• The decode_freshness_score function should now properly handle")
    print("  all the byte values found in your database")
    print("• Your GUI should display the correct freshness scores")
    print("• Sorting should work properly with decoded values")
    print()
    print("📋 Next Steps:")
    print("1. Run your SEO Assistant GUI")
    print("2. Go to Tools > Force Refresh GUI")
    print("3. Check that scores are now displaying correctly")
    print("4. Try sorting by Freshness column")
    print("5. Select items to verify detail view shows correct scores")


def test_database_fix():
    """Test that the database can handle various score types properly."""
    print("\n🗄️ Testing Database Score Handling")
    print("=" * 50)
    
    try:
        from database import ContentDatabase
        
        # Test with a temporary database
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            test_db_path = tmp_file.name
        
        db = ContentDatabase(test_db_path)
        
        # Test inserting and updating scores
        test_id = db.insert_content_idea(
            keyword="test decode fix",
            pillar="test",
            craft="test",
            proposed_angle="Testing byte decode functionality"
        )
        
        # Test various score types
        score_tests = [
            42.5,          # Normal float
            b'\xe4\xf1\x81B',  # Bytes from your database
            '123.45',      # String number
            None,          # None value
        ]
        
        for i, score in enumerate(score_tests):
            print(f"Test {i+1}: Updating with {repr(score)}")
            try:
                success = db.update_freshness_score(test_id, score)
                print(f"  Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
                
                # Verify what was stored
                content = db.get_content_by_id(test_id)
                if content:
                    stored_score = content['freshness_score']
                    print(f"  Stored: {repr(stored_score)} (type: {type(stored_score)})")
                    
                    # Decode and verify
                    decoded = decode_freshness_score(stored_score, "test")
                    print(f"  Decoded: {decoded:.2f}")
                else:
                    print("  Could not retrieve content")
                    
            except Exception as e:
                print(f"  Error: {e}")
            print()
        
        # Cleanup
        os.unlink(test_db_path)
        print("✅ Database test completed successfully")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")


if __name__ == '__main__':
    test_byte_decoding()
    test_database_fix()
    
    print("\n🎉 Test completed!")
    print("If all tests passed, your freshness score byte issue should be fixed!")
