"""
Settings Window - Content Strategist Configuration

This window provides three tabs for configuring the Content Strategist:
1. API Keys & Connections - LLM and platform credentials with test functionality
2. LLM Prompts - Prompts for analysis, writing, and idea generation
3. Business Logic - Freshness threshold, business pillars, and pillar weights
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import threading
from typing import Dict, Any
from core import config_manager
from core.llm_interface import get_llm_instance, AVAILABLE_MODELS


class SettingsWindow:
    """Settings window for Content Strategist configuration."""

    def __init__(self, master):
        self.master = master
        master.title("Content Strategist Settings")
        master.geometry("900x800")
        master.resizable(True, True)

        # Initialize variables for API key fields
        self.api_key_vars = {}
        self.platform_vars = {}
        self.pillar_weight_vars = {}
        self.connection_status_labels = {}  # Store status labels for each provider

        # Create main frame
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Create tabs
        self.create_api_keys_tab()
        self.create_prompts_tab()
        self.create_business_logic_tab()

        # Save button
        ttk.Button(main_frame, text="Save Settings", command=self.save_settings).pack(pady=10)

        # Load current settings
        self.load_settings()
    
    def create_api_keys_tab(self):
        """Create the API Keys & Connections tab."""
        api_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(api_frame, text="API Keys & Connections")

        # Create scrollable frame
        canvas = tk.Canvas(api_frame)
        scrollbar = ttk.Scrollbar(api_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        row = 0

        # SERP API Key
        ttk.Label(scrollable_frame, text="SERP API Key:", font="-weight bold").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_vars['serpapi_key'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.api_key_vars['serpapi_key'], width=40, show="*").grid(row=row, column=1, padx=5, pady=5)
        ttk.Button(scrollable_frame, text="Test", command=self.test_serpapi_connection).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # AlsoAsked API Key
        ttk.Label(scrollable_frame, text="AlsoAsked API Key:", font="-weight bold").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_vars['alsoasked_key'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.api_key_vars['alsoasked_key'], width=40, show="*").grid(row=row, column=1, padx=5, pady=5)
        ttk.Button(scrollable_frame, text="Test", command=self.test_alsoasked_connection).grid(row=row, column=2, padx=5, pady=5)
        row += 1

        # LLM Providers with Test Connection buttons
        llm_providers = [
            ('OpenAI', 'openai_key', 'openai'),
            ('Gemini', 'gemini_key', 'gemini'),
            ('Anthropic', 'anthropic_key', 'anthropic'),
            ('Groq', 'groq_key', 'groq'),
            ('OpenRouter', 'openrouter_key', 'openrouter')
        ]

        for provider_name, key_name, provider_type in llm_providers:
            # Provider label
            ttk.Label(scrollable_frame, text=f"{provider_name} API Key:", font="-weight bold").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)

            # API key entry
            self.api_key_vars[key_name] = tk.StringVar()
            ttk.Entry(scrollable_frame, textvariable=self.api_key_vars[key_name], width=40, show="*").grid(row=row, column=1, padx=5, pady=5)

            # Test connection button
            test_btn = ttk.Button(scrollable_frame, text="Test & List Models",
                                command=lambda p=provider_type: self.test_llm_connection(p))
            test_btn.grid(row=row, column=2, padx=5, pady=5)

            # Status label
            status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
            status_label.grid(row=row, column=3, padx=5, pady=5, sticky=tk.W)
            self.connection_status_labels[provider_type] = status_label

            row += 1

        # Local LLM
        ttk.Label(scrollable_frame, text="Local LLM Endpoint:", font="-weight bold").grid(row=row, column=0, sticky=tk.W, padx=5, pady=5)
        self.api_key_vars['local_llm_endpoint'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.api_key_vars['local_llm_endpoint'], width=40).grid(row=row, column=1, padx=5, pady=5)
        test_local_btn = ttk.Button(scrollable_frame, text="Test Connection",
                                  command=lambda: self.test_llm_connection('local'))
        test_local_btn.grid(row=row, column=2, padx=5, pady=5)

        # Status label for local LLM
        local_status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
        local_status_label.grid(row=row, column=3, padx=5, pady=5, sticky=tk.W)
        self.connection_status_labels['local'] = local_status_label

        row += 1

        # Add status labels for SERP and AlsoAsked APIs
        # SERP API Status
        serpapi_status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
        serpapi_status_label.grid(row=row-3, column=3, padx=5, pady=5, sticky=tk.W)  # SERP API row
        self.connection_status_labels['serpapi'] = serpapi_status_label

        # AlsoAsked API Status
        alsoasked_status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
        alsoasked_status_label.grid(row=row-2, column=3, padx=5, pady=5, sticky=tk.W)  # AlsoAsked API row
        self.connection_status_labels['alsoasked'] = alsoasked_status_label

        # Separator
        ttk.Separator(scrollable_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky='ew', pady=15)
        row += 1

        # Platform Connections
        ttk.Label(scrollable_frame, text="Platform Connections", font="-weight bold -size 12").grid(row=row, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(0, 10))
        row += 1

        # Shopify settings
        ttk.Label(scrollable_frame, text="Shopify Settings:", font="-weight bold").grid(row=row, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(10, 5))
        row += 1

        ttk.Label(scrollable_frame, text="Shop URL:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.platform_vars['shopify_url'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.platform_vars['shopify_url'], width=40).grid(row=row, column=1, padx=5, pady=2)
        row += 1

        ttk.Label(scrollable_frame, text="API Token:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.platform_vars['shopify_token'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.platform_vars['shopify_token'], width=40, show="*").grid(row=row, column=1, padx=5, pady=2)
        ttk.Button(scrollable_frame, text="Test Connection", command=self.test_shopify_connection).grid(row=row, column=2, padx=5, pady=2)

        # Status label for Shopify
        shopify_status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
        shopify_status_label.grid(row=row, column=3, padx=5, pady=2, sticky=tk.W)
        self.connection_status_labels['shopify'] = shopify_status_label

        row += 1

        # WordPress settings
        ttk.Label(scrollable_frame, text="WordPress Settings:", font="-weight bold").grid(row=row, column=0, columnspan=3, sticky=tk.W, padx=5, pady=(15, 5))
        row += 1

        ttk.Label(scrollable_frame, text="Site URL:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.platform_vars['wordpress_url'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.platform_vars['wordpress_url'], width=40).grid(row=row, column=1, padx=5, pady=2)
        row += 1

        ttk.Label(scrollable_frame, text="Username:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.platform_vars['wordpress_username'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.platform_vars['wordpress_username'], width=40).grid(row=row, column=1, padx=5, pady=2)
        row += 1

        ttk.Label(scrollable_frame, text="Password:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        self.platform_vars['wordpress_password'] = tk.StringVar()
        ttk.Entry(scrollable_frame, textvariable=self.platform_vars['wordpress_password'], width=40, show="*").grid(row=row, column=1, padx=5, pady=2)
        ttk.Button(scrollable_frame, text="Test Connection", command=self.test_wordpress_connection).grid(row=row, column=2, padx=5, pady=2)

        # Status label for WordPress
        wordpress_status_label = ttk.Label(scrollable_frame, text="Not tested", foreground="gray")
        wordpress_status_label.grid(row=row, column=3, padx=5, pady=2, sticky=tk.W)
        self.connection_status_labels['wordpress'] = wordpress_status_label

        row += 1

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_prompts_tab(self):
        """Create the LLM Prompts tab."""
        prompts_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(prompts_frame, text="LLM Prompts")

        # Instructions
        instructions = ttk.Label(prompts_frame, text="Customize the prompts used by the Content Strategist system:",
                                font="-weight bold")
        instructions.pack(anchor=tk.W, pady=(0, 15))

        # Analysis prompt (used by worker.py)
        ttk.Label(prompts_frame, text="Analysis Prompt (used by worker.py):", font="-weight bold").pack(anchor=tk.W, pady=(0, 5))
        self.analysis_prompt_text = scrolledtext.ScrolledText(prompts_frame, wrap=tk.WORD, height=8)
        self.analysis_prompt_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Writing prompt (used by worker.py)
        ttk.Label(prompts_frame, text="Writing Prompt (used by worker.py):", font="-weight bold").pack(anchor=tk.W, pady=(0, 5))
        self.writing_prompt_text = scrolledtext.ScrolledText(prompts_frame, wrap=tk.WORD, height=8)
        self.writing_prompt_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Idea generator prompt (used by idea_generator.py)
        ttk.Label(prompts_frame, text="Idea Generator Prompt (used by idea_generator.py):", font="-weight bold").pack(anchor=tk.W, pady=(0, 5))
        self.idea_generator_prompt_text = scrolledtext.ScrolledText(prompts_frame, wrap=tk.WORD, height=8)
        self.idea_generator_prompt_text.pack(fill=tk.BOTH, expand=True)
    
    def create_business_logic_tab(self):
        """Create the Business Logic tab."""
        business_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(business_frame, text="Business Logic")

        # Instructions
        instructions = ttk.Label(business_frame, text="Configure the core strategy settings for content planning:",
                                font="-weight bold")
        instructions.pack(anchor=tk.W, pady=(0, 15))

        # Freshness threshold
        threshold_section = ttk.LabelFrame(business_frame, text="Freshness Threshold", padding="10")
        threshold_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(threshold_section, text="Minimum score for content to be selected for execution (0-100):").pack(anchor=tk.W, pady=(0, 5))

        threshold_frame = ttk.Frame(threshold_section)
        threshold_frame.pack(fill=tk.X, pady=(5, 0))

        self.freshness_threshold_var = tk.DoubleVar(value=70.0)
        threshold_scale = ttk.Scale(threshold_frame, from_=0, to=100, variable=self.freshness_threshold_var,
                                   orient=tk.HORIZONTAL, length=300)
        threshold_scale.pack(side=tk.LEFT)

        threshold_spinbox = ttk.Spinbox(threshold_frame, from_=0, to=100, textvariable=self.freshness_threshold_var,
                                       width=8, increment=1.0)
        threshold_spinbox.pack(side=tk.LEFT, padx=(10, 0))

        # Freshness Score Calculation Settings
        score_calc_section = ttk.LabelFrame(business_frame, text="Freshness Score Calculation", padding="10")
        score_calc_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(score_calc_section, text="Configure how freshness scores are calculated:", font="-weight bold").pack(anchor=tk.W, pady=(0, 10))

        # Time Score Weight
        time_weight_frame = ttk.Frame(score_calc_section)
        time_weight_frame.pack(fill=tk.X, pady=2)

        ttk.Label(time_weight_frame, text="Time Score Weight:", width=20).pack(side=tk.LEFT, padx=(0, 5))
        self.time_score_weight_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(time_weight_frame, from_=0.0, to=1.0, textvariable=self.time_score_weight_var,
                   width=8, increment=0.1, format="%.1f").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(time_weight_frame, text="(0.0-1.0, how much time since last similar post matters)").pack(side=tk.LEFT)

        # Uniqueness Score Weight
        unique_weight_frame = ttk.Frame(score_calc_section)
        unique_weight_frame.pack(fill=tk.X, pady=2)

        ttk.Label(unique_weight_frame, text="Uniqueness Score Weight:", width=20).pack(side=tk.LEFT, padx=(0, 5))
        self.uniqueness_score_weight_var = tk.DoubleVar(value=0.5)
        ttk.Spinbox(unique_weight_frame, from_=0.0, to=1.0, textvariable=self.uniqueness_score_weight_var,
                   width=8, increment=0.1, format="%.1f").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(unique_weight_frame, text="(0.0-1.0, how much content uniqueness matters)").pack(side=tk.LEFT)

        # Angle Bonus Max Points
        angle_bonus_frame = ttk.Frame(score_calc_section)
        angle_bonus_frame.pack(fill=tk.X, pady=2)

        ttk.Label(angle_bonus_frame, text="Angle Bonus Max Points:", width=20).pack(side=tk.LEFT, padx=(0, 5))
        self.angle_bonus_max_var = tk.IntVar(value=15)
        ttk.Spinbox(angle_bonus_frame, from_=0, to=30, textvariable=self.angle_bonus_max_var,
                   width=8, increment=1).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(angle_bonus_frame, text="(0-30, bonus points for having a proposed angle)").pack(side=tk.LEFT)

        # Business pillars
        pillars_section = ttk.LabelFrame(business_frame, text="Business Pillars", padding="10")
        pillars_section.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        ttk.Label(pillars_section, text="Define your crafts and pillar keywords (Format: Craft Name = Pillar1, Pillar2, Pillar3):").pack(anchor=tk.W, pady=(0, 5))

        self.business_pillars_text = scrolledtext.ScrolledText(pillars_section, wrap=tk.WORD, height=6)
        self.business_pillars_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Example text
        example_text = """Example:
Grooming = shaving brushes, beard oil, natural soap, aftershave balm
Leather Goods = wallets, belts, dopp kits, watch straps
Fragrance = cologne, perfume, essential oils"""
        ttk.Label(pillars_section, text=example_text, foreground="gray").pack(anchor=tk.W)

        # Pillar weights
        weights_section = ttk.LabelFrame(business_frame, text="Pillar Weights", padding="10")
        weights_section.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(weights_section, text="Weight multipliers for each craft (higher = more priority):").pack(anchor=tk.W, pady=(0, 5))

        # Frame for dynamic pillar weights
        self.pillar_weights_frame = ttk.Frame(weights_section)
        self.pillar_weights_frame.pack(fill=tk.X, pady=(5, 10))

        # Button to refresh pillar weights
        ttk.Button(weights_section, text="🔄 Update Pillar Weights from Business Pillars",
                  command=self.update_pillar_weights).pack(pady=5)
    
    def update_pillar_weights(self):
        """Update the pillar weights section based on the business pillars text."""
        # Clear existing weights
        for widget in self.pillar_weights_frame.winfo_children():
            widget.destroy()
        self.pillar_weight_vars.clear()

        # Parse business pillars
        pillars_text = self.business_pillars_text.get('1.0', tk.END).strip()
        crafts = []

        for line in pillars_text.split('\n'):
            line = line.strip()
            if '=' in line:
                craft = line.split('=', 1)[0].strip()
                if craft:
                    crafts.append(craft)

        if not crafts:
            ttk.Label(self.pillar_weights_frame, text="No crafts found. Please define business pillars first.",
                     foreground="gray").pack(pady=10)
            return

        # Create weight controls for each craft
        for i, craft in enumerate(crafts):
            craft_frame = ttk.Frame(self.pillar_weights_frame)
            craft_frame.pack(fill=tk.X, pady=2)

            ttk.Label(craft_frame, text=f"{craft}:", width=20).pack(side=tk.LEFT, padx=(0, 5))

            weight_var = tk.DoubleVar(value=1.0)
            self.pillar_weight_vars[craft] = weight_var

            weight_spinbox = ttk.Spinbox(craft_frame, from_=0.1, to=5.0,
                                       textvariable=weight_var, width=8, increment=0.1, format="%.1f")
            weight_spinbox.pack(side=tk.LEFT, padx=(0, 5))

            ttk.Label(craft_frame, text="(1.0 = normal, >1.0 = higher priority)").pack(side=tk.LEFT)

    def test_llm_connection(self, provider_type: str):
        """Test connection to an LLM provider and list models."""
        # Update status to testing
        if provider_type in self.connection_status_labels:
            self.connection_status_labels[provider_type].config(text="Testing...", foreground="orange")

        def test_thread():
            error_msg = None
            success_msg = None

            try:
                # Get API key
                if provider_type == 'local':
                    api_key = None
                    endpoint = self.api_key_vars['local_llm_endpoint'].get().strip()
                    if not endpoint:
                        error_msg = "Please enter a Local LLM endpoint URL"
                        return
                else:
                    key_name = f"{provider_type}_key"
                    api_key = self.api_key_vars[key_name].get().strip()
                    if not api_key:
                        error_msg = f"Please enter a {provider_type.title()} API key"
                        return
                    endpoint = None

                # Create LLM instance
                llm = get_llm_instance(provider_type, api_key=api_key, api_endpoint=endpoint)

                # Test connection
                success = llm.test_connection()
                if not success:
                    error_msg = f"Failed to connect to {provider_type.title()}"
                    return

                # List models
                models = llm.list_models()
                if models:
                    # Update AVAILABLE_MODELS
                    AVAILABLE_MODELS[provider_type] = models
                    # Save to config
                    config_manager.save_models_list(provider_type, models)

                    success_msg = f"✅ {provider_type.title()} connection successful!\nFound {len(models)} models.\nModels saved to configuration."

                    # Update status label
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H:%M")
                    self.master.after(0, lambda: self.connection_status_labels[provider_type].config(
                        text=f"✅ Success ({timestamp})", foreground="green"))
                else:
                    success_msg = f"Connection successful but no models found for {provider_type.title()}"
                    self.master.after(0, lambda: self.connection_status_labels[provider_type].config(
                        text=f"⚠️ No models", foreground="orange"))

            except Exception as e:
                error_msg = f"Error testing {provider_type.title()} connection:\n{str(e)}"

            finally:
                # Update UI in main thread
                if error_msg:
                    self.master.after(0, lambda msg=error_msg: messagebox.showerror("Error", msg))
                    self.master.after(0, lambda: self.connection_status_labels[provider_type].config(
                        text="❌ Failed", foreground="red"))
                elif success_msg:
                    self.master.after(0, lambda msg=success_msg: messagebox.showinfo("Success", msg))

        threading.Thread(target=test_thread, daemon=True).start()

    def test_shopify_connection(self):
        """Test Shopify connection."""
        # Update status to testing
        self.connection_status_labels['shopify'].config(text="Testing...", foreground="orange")

        def test_thread():
            try:
                shop_url = self.platform_vars['shopify_url'].get().strip()
                api_token = self.platform_vars['shopify_token'].get().strip()

                if not shop_url or not api_token:
                    self.master.after(0, lambda: messagebox.showerror("Error", "Please enter both Shopify URL and API token"))
                    self.master.after(0, lambda: self.connection_status_labels['shopify'].config(text="❌ Missing credentials", foreground="red"))
                    return

                # Test connection using actual shopify_poster module
                from core import shopify_poster
                success = shopify_poster.test_connection(shop_url, api_token)

                if success:
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H:%M")

                    # Update config to mark connection as working
                    config_manager.set_setting('SHOPIFY', 'connection_ok', 'true')

                    self.master.after(0, lambda: messagebox.showinfo("Success", "Shopify connection test successful!"))
                    self.master.after(0, lambda: self.connection_status_labels['shopify'].config(text=f"✅ Success ({timestamp})", foreground="green"))
                else:
                    # Update config to mark connection as failed
                    config_manager.set_setting('SHOPIFY', 'connection_ok', 'false')

                    self.master.after(0, lambda: messagebox.showerror("Error", "Shopify connection test failed"))
                    self.master.after(0, lambda: self.connection_status_labels['shopify'].config(text="❌ Failed", foreground="red"))

            except Exception as e:
                error_msg = f"Error testing Shopify connection: {e}"

                # Update config to mark connection as failed
                config_manager.set_setting('SHOPIFY', 'connection_ok', 'false')

                self.master.after(0, lambda: messagebox.showerror("Error", error_msg))
                self.master.after(0, lambda: self.connection_status_labels['shopify'].config(text="❌ Failed", foreground="red"))

        threading.Thread(target=test_thread, daemon=True).start()

    def test_wordpress_connection(self):
        """Test WordPress connection."""
        # Update status to testing
        self.connection_status_labels['wordpress'].config(text="Testing...", foreground="orange")

        def test_thread():
            try:
                site_url = self.platform_vars['wordpress_url'].get().strip()
                username = self.platform_vars['wordpress_username'].get().strip()
                password = self.platform_vars['wordpress_password'].get().strip()

                if not site_url or not username or not password:
                    self.master.after(0, lambda: messagebox.showerror("Error", "Please enter all WordPress credentials"))
                    self.master.after(0, lambda: self.connection_status_labels['wordpress'].config(text="❌ Missing credentials", foreground="red"))
                    return

                # Test connection using actual wordpress_poster module
                from core import wordpress_poster
                success = wordpress_poster.test_connection(site_url, username, password)

                if success:
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H:%M")

                    # Update config to mark connection as working
                    config_manager.set_setting('WORDPRESS', 'connection_ok', 'true')

                    self.master.after(0, lambda: messagebox.showinfo("Success", "WordPress connection test successful!"))
                    self.master.after(0, lambda: self.connection_status_labels['wordpress'].config(text=f"✅ Success ({timestamp})", foreground="green"))
                else:
                    # Update config to mark connection as failed
                    config_manager.set_setting('WORDPRESS', 'connection_ok', 'false')

                    self.master.after(0, lambda: messagebox.showerror("Error", "WordPress connection test failed"))
                    self.master.after(0, lambda: self.connection_status_labels['wordpress'].config(text="❌ Failed", foreground="red"))

            except Exception as e:
                error_msg = f"Error testing WordPress connection: {e}"

                # Update config to mark connection as failed
                config_manager.set_setting('WORDPRESS', 'connection_ok', 'false')

                self.master.after(0, lambda: messagebox.showerror("Error", error_msg))
                self.master.after(0, lambda: self.connection_status_labels['wordpress'].config(text="❌ Failed", foreground="red"))

        threading.Thread(target=test_thread, daemon=True).start()

    def test_serpapi_connection(self):
        """Test SERP API connection."""
        # Update status to testing
        self.connection_status_labels['serpapi'].config(text="Testing...", foreground="orange")

        def test_thread():
            try:
                api_key = self.api_key_vars['serpapi_key'].get().strip()

                if not api_key:
                    self.master.after(0, lambda: messagebox.showerror("Error", "Please enter SERP API key"))
                    self.master.after(0, lambda: self.connection_status_labels['serpapi'].config(text="❌ Missing key", foreground="red"))
                    return

                # Test connection using actual serp_fetcher module
                from core import serp_fetcher
                success = serp_fetcher.test_connection(api_key)

                if success:
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H:%M")

                    # Update config to mark connection as working
                    config_manager.set_setting('API_KEYS', 'serpapi_connection_ok', 'true')

                    self.master.after(0, lambda: messagebox.showinfo("Success", "SERP API connection test successful!"))
                    self.master.after(0, lambda: self.connection_status_labels['serpapi'].config(text=f"✅ Success ({timestamp})", foreground="green"))
                else:
                    # Update config to mark connection as failed
                    config_manager.set_setting('API_KEYS', 'serpapi_connection_ok', 'false')

                    self.master.after(0, lambda: messagebox.showerror("Error", "SERP API connection test failed"))
                    self.master.after(0, lambda: self.connection_status_labels['serpapi'].config(text="❌ Failed", foreground="red"))

            except Exception as e:
                error_msg = f"Error testing SERP API connection: {e}"

                # Update config to mark connection as failed
                config_manager.set_setting('API_KEYS', 'serpapi_connection_ok', 'false')

                self.master.after(0, lambda: messagebox.showerror("Error", error_msg))
                self.master.after(0, lambda: self.connection_status_labels['serpapi'].config(text="❌ Failed", foreground="red"))

        threading.Thread(target=test_thread, daemon=True).start()

    def test_alsoasked_connection(self):
        """Test AlsoAsked API connection."""
        # Update status to testing
        self.connection_status_labels['alsoasked'].config(text="Testing...", foreground="orange")

        def test_thread():
            try:
                api_key = self.api_key_vars['alsoasked_key'].get().strip()

                if not api_key:
                    self.master.after(0, lambda: messagebox.showerror("Error", "Please enter AlsoAsked API key"))
                    self.master.after(0, lambda: self.connection_status_labels['alsoasked'].config(text="❌ Missing key", foreground="red"))
                    return

                # Test connection using actual alsoasked_fetcher module
                from core import alsoasked_fetcher
                success = alsoasked_fetcher.test_connection(api_key)

                if success:
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H:%M")

                    # Update config to mark connection as working
                    config_manager.set_setting('API_KEYS', 'alsoasked_connection_ok', 'true')

                    self.master.after(0, lambda: messagebox.showinfo("Success", "AlsoAsked API connection test successful!"))
                    self.master.after(0, lambda: self.connection_status_labels['alsoasked'].config(text=f"✅ Success ({timestamp})", foreground="green"))
                else:
                    # Update config to mark connection as failed
                    config_manager.set_setting('API_KEYS', 'alsoasked_connection_ok', 'false')

                    self.master.after(0, lambda: messagebox.showerror("Error", "AlsoAsked API connection test failed"))
                    self.master.after(0, lambda: self.connection_status_labels['alsoasked'].config(text="❌ Failed", foreground="red"))

            except Exception as e:
                error_msg = f"Error testing AlsoAsked API connection: {e}"

                # Update config to mark connection as failed
                config_manager.set_setting('API_KEYS', 'alsoasked_connection_ok', 'false')

                self.master.after(0, lambda: messagebox.showerror("Error", error_msg))
                self.master.after(0, lambda: self.connection_status_labels['alsoasked'].config(text="❌ Failed", foreground="red"))

        threading.Thread(target=test_thread, daemon=True).start()

    def load_settings(self):
        """Load current settings from config."""
        try:
            # Load API keys
            api_keys = {
                'serpapi_key': 'serpapi_key',
                'alsoasked_key': 'alsoasked_key',
                'openai_key': 'openai_key',
                'gemini_key': 'gemini_key',
                'anthropic_key': 'anthropic_key',
                'groq_key': 'groq_key',
                'openrouter_key': 'openrouter_key',
                'local_llm_endpoint': 'local_llm_endpoint'
            }

            for var_name, config_key in api_keys.items():
                value = config_manager.get_api_key(config_key) or ''
                self.api_key_vars[var_name].set(value)

            # Load platform settings
            platform_settings = {
                'shopify_url': ('SHOPIFY', 'shop_url'),
                'shopify_token': ('SHOPIFY', 'api_token'),
                'wordpress_url': ('WORDPRESS', 'site_url'),
                'wordpress_username': ('WORDPRESS', 'username'),
                'wordpress_password': ('WORDPRESS', 'password')
            }

            for var_name, (section, key) in platform_settings.items():
                value = config_manager.get_setting(section, key) or ''
                self.platform_vars[var_name].set(value)

            # Load prompts
            prompts = {
                'analysis_prompt': self.analysis_prompt_text,
                'writing_prompt': self.writing_prompt_text,
                'idea_generator_prompt': self.idea_generator_prompt_text
            }

            for prompt_name, text_widget in prompts.items():
                text_widget.delete('1.0', tk.END)
                prompt_content = config_manager.get_prompt(prompt_name) or ''
                text_widget.insert('1.0', prompt_content)

            # Load business logic settings
            self.freshness_threshold_var.set(float(config_manager.get_pipeline_setting('freshness_threshold') or 70.0))

            # Load freshness score calculation settings
            self.time_score_weight_var.set(float(config_manager.get_pipeline_setting('time_score_weight') or 0.5))
            self.uniqueness_score_weight_var.set(float(config_manager.get_pipeline_setting('uniqueness_score_weight') or 0.5))
            self.angle_bonus_max_var.set(int(config_manager.get_pipeline_setting('angle_bonus_max_points') or 15))

            business_pillars = config_manager.get_pipeline_setting('business_pillars') or """Grooming = shaving brushes, beard oil, natural soap, aftershave balm
Leather Goods = wallets, belts, dopp kits, watch straps
Fragrance = cologne, perfume, essential oils"""
            self.business_pillars_text.delete('1.0', tk.END)
            self.business_pillars_text.insert('1.0', business_pillars)

            # Load pillar weights and update the weights section
            self.update_pillar_weights()
            pillar_weights_str = config_manager.get_pipeline_setting('pillar_weights') or '{}'
            try:
                pillar_weights = json.loads(pillar_weights_str)
                for craft, weight in pillar_weights.items():
                    if craft in self.pillar_weight_vars:
                        self.pillar_weight_vars[craft].set(float(weight))
            except (json.JSONDecodeError, ValueError):
                pass  # Use defaults if parsing fails

            # Load connection status for APIs
            self.load_connection_status()

        except Exception as e:
            messagebox.showerror("Error", f"Error loading settings: {e}")

    def load_connection_status(self):
        """Load and display saved connection status for APIs."""
        try:
            # Load SERP API status
            serpapi_status = config_manager.get_setting('API_KEYS', 'serpapi_connection_ok')
            if serpapi_status == 'true':
                self.connection_status_labels['serpapi'].config(text="✅ Previously tested", foreground="green")
            elif serpapi_status == 'false':
                self.connection_status_labels['serpapi'].config(text="❌ Previously failed", foreground="red")

            # Load AlsoAsked API status
            alsoasked_status = config_manager.get_setting('API_KEYS', 'alsoasked_connection_ok')
            if alsoasked_status == 'true':
                self.connection_status_labels['alsoasked'].config(text="✅ Previously tested", foreground="green")
            elif alsoasked_status == 'false':
                self.connection_status_labels['alsoasked'].config(text="❌ Previously failed", foreground="red")

            # Load Shopify status
            shopify_status = config_manager.get_setting('SHOPIFY', 'connection_ok')
            if shopify_status == 'true':
                self.connection_status_labels['shopify'].config(text="✅ Previously tested", foreground="green")
            elif shopify_status == 'false':
                self.connection_status_labels['shopify'].config(text="❌ Previously failed", foreground="red")

            # Load WordPress status
            wordpress_status = config_manager.get_setting('WORDPRESS', 'connection_ok')
            if wordpress_status == 'true':
                self.connection_status_labels['wordpress'].config(text="✅ Previously tested", foreground="green")
            elif wordpress_status == 'false':
                self.connection_status_labels['wordpress'].config(text="❌ Previously failed", foreground="red")

        except Exception as e:
            print(f"Error loading connection status: {e}")

    def save_settings(self):
        """Save all settings to config."""
        try:
            # Prepare settings dictionary
            settings_to_update = {}

            # API Keys
            for var_name, var_obj in self.api_key_vars.items():
                settings_to_update[f'API_KEYS.{var_name}'] = var_obj.get().strip()

            # Platform settings
            platform_mapping = {
                'shopify_url': 'SHOPIFY.shop_url',
                'shopify_token': 'SHOPIFY.api_token',
                'wordpress_url': 'WORDPRESS.site_url',
                'wordpress_username': 'WORDPRESS.username',
                'wordpress_password': 'WORDPRESS.password'
            }

            for var_name, config_key in platform_mapping.items():
                settings_to_update[config_key] = self.platform_vars[var_name].get().strip()

            # Prompts
            settings_to_update['PROMPTS.analysis_prompt'] = self.analysis_prompt_text.get('1.0', tk.END).strip()
            settings_to_update['PROMPTS.writing_prompt'] = self.writing_prompt_text.get('1.0', tk.END).strip()
            settings_to_update['PROMPTS.idea_generator_prompt'] = self.idea_generator_prompt_text.get('1.0', tk.END).strip()

            # Business Logic
            settings_to_update['PIPELINE_SETTINGS.freshness_threshold'] = str(self.freshness_threshold_var.get())
            settings_to_update['PIPELINE_SETTINGS.business_pillars'] = self.business_pillars_text.get('1.0', tk.END).strip()

            # Freshness Score Calculation Settings
            settings_to_update['PIPELINE_SETTINGS.time_score_weight'] = str(self.time_score_weight_var.get())
            settings_to_update['PIPELINE_SETTINGS.uniqueness_score_weight'] = str(self.uniqueness_score_weight_var.get())
            settings_to_update['PIPELINE_SETTINGS.angle_bonus_max_points'] = str(self.angle_bonus_max_var.get())

            # Pillar weights as JSON
            pillar_weights = {craft: var.get() for craft, var in self.pillar_weight_vars.items()}
            settings_to_update['PIPELINE_SETTINGS.pillar_weights'] = json.dumps(pillar_weights)

            # Update config using the multiple settings update method
            config_manager.update_multiple_settings(settings_to_update)

            messagebox.showinfo("Success", "Settings saved successfully!")
            self.master.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Error saving settings: {e}")
