## Instructions for Fixing execute_planned_job Method in main_window.py

The execute_planned_job method needs to be updated to use the new checkbox variables instead of the old data_source_var. Here's what needs to change:

### Current Issue:
The method likely has lines like:
```python
data_source = self.data_source_var.get()
```

### Required Fix:
Replace the data source handling with:
```python
# Get multiple data source settings
use_serpapi = self.use_serpapi_var.get()
use_alsoasked = self.use_alsoasked_var.get()
use_llm_suggestion = self.use_llm_suggestion_var.get()

# Get publishing settings
platform = self.publish_platform_var.get()
publish_as_draft = self.publish_as_draft_var.get()
```

### Update the worker call:
Replace the worker.execute_job call to use the new parameters:
```python
result = self.worker.execute_job(
    job,
    run_data_collection=run_data_collection,
    run_blog_writing=run_blog_writing,
    run_publish=run_publish,
    use_serpapi=use_serpapi,
    use_alsoasked=use_alsoasked,
    use_llm_suggestion=use_llm_suggestion,
    publish_platform=platform,
    publish_as_draft=publish_as_draft
)
```

### Update the logging:
```python
self.log_message(f"Data Sources: SerpApi={use_serpapi}, AlsoAsked={use_alsoasked}, LLM={use_llm_suggestion}", "info")
self.log_message(f"Platform: {platform}, Draft: {publish_as_draft}", "info")
```

The worker.py file is already correctly implemented to accept these parameters.
